import logging
from django.utils import timezone
from rest_framework.authentication import Base<PERSON><PERSON>entication, SessionAuthentication, TokenAuthentication
from rest_framework.exceptions import AuthenticationFailed
from oauth2_provider.models import AccessToken
from .models import DeviceToken
from .utils import log_security_event, get_client_ip

logger = logging.getLogger(__name__)


def _get_user_status_validator():
    """Lazy import to avoid circular imports"""
    from user.auth_utils import UserStatusValidator
    return UserStatusValidator


def _log_authentication_attempt(*args, **kwargs):
    """Lazy import to avoid circular imports"""
    from user.auth_utils import log_authentication_attempt
    return log_authentication_attempt(*args, **kwargs)


class OAuth2Authentication(BaseAuthentication):
    """
    Custom OAuth2 authentication class with enhanced security
    """
    
    def authenticate(self, request):
        """
        Authenticate using OAuth2 Bearer token
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        
        if not auth_header.startswith('Bearer '):
            return None
        
        token = auth_header.split(' ')[1] if len(auth_header.split(' ')) > 1 else None
        if not token:
            return None
        
        try:
            # First check if token is blacklisted using the new token invalidation service
            from .token_invalidation_service import token_invalidation_service
            if token_invalidation_service.is_token_blacklisted(token):
                log_security_event(
                    event_type='blacklisted_token_usage',
                    description='Attempt to use blacklisted token',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    metadata={'token': token[:10] + '...'}
                )
                raise AuthenticationFailed('Token has been invalidated')

            # Try to find the access token
            access_token = AccessToken.objects.select_related('user', 'application').get(token=token)

            # Check if token is valid
            if not access_token.is_valid():
                log_security_event(
                    event_type='invalid_token_usage',
                    description='Attempt to use invalid/expired token',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    metadata={'token': token[:10] + '...'}
                )
                raise AuthenticationFailed('Token is invalid or expired')

            # Validate user status before allowing authentication
            user = access_token.user
            UserStatusValidator = _get_user_status_validator()
            validation_result = UserStatusValidator.validate_user_status(user, log_violations=True)

            if not validation_result['is_valid']:
                # Log authentication attempt with user status violation
                _log_authentication_attempt(
                    user=user,
                    success=False,
                    method='oauth2_token',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    error_code=validation_result['error_code']
                )

                # Raise authentication failed with specific error
                raise AuthenticationFailed(
                    f"{validation_result['error_message']}: {validation_result['error_details']}"
                )

            # Log successful authentication
            _log_authentication_attempt(
                user=user,
                success=True,
                method='oauth2_token',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            return (access_token.user, access_token)

        except AccessToken.DoesNotExist:
            # Try JWT token validation using the new JWT rotation service
            from .jwt_rotation_service import jwt_rotation_service
            is_valid, jwt_payload = jwt_rotation_service.validate_access_token(token)

            if is_valid and jwt_payload:
                try:
                    from django.contrib.auth import get_user_model
                    User = get_user_model()
                    user = User.objects.get(id=jwt_payload['sub'])

                    # Validate user status for JWT authentication
                    UserStatusValidator = _get_user_status_validator()
                    validation_result = UserStatusValidator.validate_user_status(user, log_violations=True)

                    if not validation_result['is_valid']:
                        # Log authentication attempt with user status violation
                        _log_authentication_attempt(
                            user=user,
                            success=False,
                            method='jwt_token',
                            ip_address=get_client_ip(request),
                            user_agent=request.META.get('HTTP_USER_AGENT', ''),
                            error_code=validation_result['error_code']
                        )

                        # Raise authentication failed with specific error
                        raise AuthenticationFailed(
                            f"{validation_result['error_message']}: {validation_result['error_details']}"
                        )

                    # Additional validation: Check if JWT token status matches current user status
                    if 'user_status' in jwt_payload:
                        token_status = jwt_payload['user_status']
                        current_status = UserStatusValidator.get_user_status_summary(user)

                        # Check for critical status changes since token was issued
                        critical_changes = []
                        if token_status.get('is_active', True) and not current_status['is_active']:
                            critical_changes.append('account_deactivated')
                        if not token_status.get('is_deleted', False) and current_status['is_deleted']:
                            critical_changes.append('account_deleted')
                        if not token_status.get('is_locked', False) and current_status['is_locked']:
                            critical_changes.append('account_locked')

                        if critical_changes:
                            logger.warning(f"JWT token rejected due to status changes for {user.email}: {critical_changes}")
                            raise AuthenticationFailed(
                                "Token is no longer valid due to account status changes. Please login again."
                            )

                    # Log successful JWT authentication
                    _log_authentication_attempt(
                        user=user,
                        success=True,
                        method='jwt_token',
                        ip_address=get_client_ip(request),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')
                    )

                    return (user, jwt_payload)
                except User.DoesNotExist:
                    pass
            
            log_security_event(
                event_type='invalid_token_usage',
                description='Attempt to use non-existent token',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                metadata={'token': token[:10] + '...'}
            )
            raise AuthenticationFailed('Invalid token')
        
        except Exception as e:
            logger.error(f"OAuth2 authentication error: {str(e)}")
            raise AuthenticationFailed('Authentication failed')
class DeviceAuthenticationService:
    """
    Service for device-based authentication and trust management
    """
    
    @staticmethod
    def register_device(user, device_id, device_name, device_type, request):
        """
        Register a new device for the user
        """
        try:
            from .utils import generate_device_fingerprint
            
            fingerprint = generate_device_fingerprint(request)
            ip_address = get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            device, created = DeviceToken.objects.get_or_create(
                user=user,
                device_id=device_id,
                defaults={
                    'device_name': device_name,
                    'device_type': device_type,
                    'fingerprint': fingerprint,
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                }
            )
            
            if not created:
                # Update existing device
                device.device_name = device_name
                device.device_type = device_type
                device.fingerprint = fingerprint
                device.ip_address = ip_address
                device.user_agent = user_agent
                device.last_seen = timezone.now()
                device.save()
            
            log_security_event(
                user=user,
                event_type='device_registered',
                description=f'Device registered: {device_name}',
                ip_address=ip_address,
                user_agent=user_agent,
                device_id=device_id,
                metadata={
                    'device_type': device_type,
                    'fingerprint': fingerprint,
                    'created': created
                }
            )
            
            return device
        except Exception as e:
            logger.error(f"Device registration error: {str(e)}")
            return None
    
    @staticmethod
    def trust_device(user, device_id, trust_duration_days=30):
        """
        Mark a device as trusted for a specified duration
        """
        try:
            device = DeviceToken.objects.get(user=user, device_id=device_id)
            device.is_trusted = True
            device.trust_expires = timezone.now() + timezone.timedelta(days=trust_duration_days)
            device.save()
            
            log_security_event(
                user=user,
                event_type='device_trusted',
                description=f'Device trusted: {device.device_name}',
                device_id=device_id,
                metadata={
                    'trust_duration_days': trust_duration_days,
                    'trust_expires': device.trust_expires.isoformat()
                }
            )
            
            return True
        except DeviceToken.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"Device trust error: {str(e)}")
            return False
    
    @staticmethod
    def revoke_device_trust(user, device_id):
        """
        Revoke trust for a device
        """
        try:
            device = DeviceToken.objects.get(user=user, device_id=device_id)
            device.is_trusted = False
            device.trust_expires = None
            device.save()
            
            log_security_event(
                user=user,
                event_type='device_trust_revoked',
                description=f'Device trust revoked: {device.device_name}',
                device_id=device_id
            )
            
            return True
        except DeviceToken.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"Device trust revocation error: {str(e)}")
            return False


class EnhancedSessionAuthentication(SessionAuthentication):
    """
    Enhanced session authentication with user status validation
    """

    def authenticate(self, request):
        """
        Authenticate using session with user status validation
        """
        # Call parent authentication first
        auth_result = super().authenticate(request)

        if auth_result is None:
            return None

        user, _ = auth_result

        # Validate user status
        UserStatusValidator = _get_user_status_validator()
        validation_result = UserStatusValidator.validate_user_status(user, log_violations=True)

        if not validation_result['is_valid']:
            # Log authentication attempt with user status violation
            _log_authentication_attempt(
                user=user,
                success=False,
                method='session',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                error_code=validation_result['error_code']
            )

            # Raise authentication failed with specific error
            raise AuthenticationFailed(
                f"{validation_result['error_message']}: {validation_result['error_details']}"
            )

        # Log successful session authentication
        _log_authentication_attempt(
            user=user,
            success=True,
            method='session',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return auth_result


class EnhancedTokenAuthentication(TokenAuthentication):
    """
    Enhanced token authentication with user status validation
    """

    def authenticate(self, request):
        """
        Authenticate using token with user status validation
        """
        # Call parent authentication first
        auth_result = super().authenticate(request)

        if auth_result is None:
            return None

        user, _ = auth_result

        # Validate user status
        UserStatusValidator = _get_user_status_validator()
        validation_result = UserStatusValidator.validate_user_status(user, log_violations=True)

        if not validation_result['is_valid']:
            # Log authentication attempt with user status violation
            _log_authentication_attempt(
                user=user,
                success=False,
                method='token',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                error_code=validation_result['error_code']
            )

            # Raise authentication failed with specific error
            raise AuthenticationFailed(
                f"{validation_result['error_message']}: {validation_result['error_details']}"
            )

        # Log successful token authentication
        _log_authentication_attempt(
            user=user,
            success=True,
            method='token',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return auth_result


