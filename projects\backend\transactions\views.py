# views.py
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from oauth2_auth.authentication import OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication
from oauth2_auth.permissions import (
    AnyRolePermission,
)
from django.utils import timezone
from django.db.models import Sum
from .models import Transaction, MonthlyExpense
from .serializers import (
    TransactionSerializer,
    MonthlyExpenseSerializer,
    YearlyExpenseSerializer,
    MonthlyExpenseSummarySerializer,
)
from decimal import Decimal
import time
from agritram.contract import (
    mint_tokens,
    transfer_tokens,
    burn_tokens,
    convert_ktt_tokens_to_kct,
    convert_kct_tokens_to_ktt,
)
from agritram.algorand_client import (
    app_client,
    ALGORAND_ASSET_ID_KCT,
    ASA_DECIMALS,
    ALGORAND_ASSET_ID_KTT,
)
from django.db import models
from agritram.message_utils import handle_exception_with_logging, StandardSuccessResponse
from agritram.exceptions import raise_validation_error, raise_business_logic_error, raise_not_found_error


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can view their own transactions
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def transaction_list(request):
    """
    Get transactions for the authenticated user.
    Business Rule: Users can only see their own transactions, admins can see all.
    """
    if request.user.role == 'admin':
        # Admins can see all transactions
        transactions = Transaction.objects.all().order_by("-timestamp")
    else:
        # Users can only see their own transactions
        transactions = Transaction.objects.filter(user=request.user).order_by("-timestamp")

    serializer = TransactionSerializer(transactions, many=True)
    return StandardSuccessResponse.data_retrieved(
        message="Transactions retrieved successfully",
        details=f"Retrieved {len(serializer.data)} transaction records",
        data=serializer.data
    )


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can view their recent transactions
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def recent_transactions(request):
    """
    Get recent transactions for the authenticated user.
    Business Rule: Users can only see their own transactions.
    """
    if request.user.role == 'admin':
        # Admins can see all recent transactions
        transactions = Transaction.objects.all().order_by("-timestamp")[:10]
    else:
        # Users can only see their own recent transactions
        transactions = Transaction.objects.filter(user=request.user).order_by("-timestamp")[:10]

    serializer = TransactionSerializer(transactions, many=True)
    return StandardSuccessResponse.data_retrieved(
        message="Recent transactions retrieved successfully",
        details=f"Retrieved {len(serializer.data)} recent transaction records",
        data=serializer.data
    )


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can view their own expenses
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def monthly_expenses(request):
    """
    Get monthly expenses for the authenticated user.
    Business Rule: Users can only see their own expenses.
    """
    if request.user.role == 'admin':
        # Admins can see all expenses
        expenses = MonthlyExpense.objects.all().order_by("-year", "-month")
    else:
        # Users can only see their own expenses
        expenses = MonthlyExpense.objects.filter(user=request.user).order_by("-year", "-month")

    serializer = MonthlyExpenseSerializer(expenses, many=True)
    return StandardSuccessResponse.data_retrieved(
        message="Monthly expenses retrieved successfully",
        details=f"Retrieved {len(serializer.data)} monthly expense records",
        data=serializer.data
    )


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can view their own expense summary
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def yearly_expense_summary(request):
    """
    Get yearly expense summary for the authenticated user.
    Business Rule: Users can only see their own stats, admins see global stats.
    """
    year = request.query_params.get("year", timezone.now().year)

    if request.user.role == 'admin':
        # Admins can see global expense summary
        expenses = MonthlyExpense.objects.filter(year=year).order_by("month")
    else:
        # Users can only see their own expense summary
        expenses = MonthlyExpense.objects.filter(user=request.user, year=year).order_by("month")

    # Initialize monthly data with zeros
    monthly_data = [0] * 12
    for expense in expenses:
        monthly_data[expense.month - 1] = float(expense.total_amount)

    data = {
        "year": int(year),
        "monthly_data": monthly_data,
        "labels": [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December",
        ],
    }

    serializer = YearlyExpenseSerializer(data)
    return StandardSuccessResponse.data_retrieved(
        message="Yearly expense summary retrieved successfully",
        details=f"Retrieved expense summary for year {data['year']}",
        data=serializer.data
    )


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can view their current year expenses
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def current_year_expenses(request):
    """
    Get current year expenses for the authenticated user.
    Business Rule: Users can only see their own expenses.
    """
    current_year = timezone.now().year

    if request.user.role == 'admin':
        # Admins can see all current year expenses
        expenses = MonthlyExpense.objects.filter(year=current_year).order_by("month")
    else:
        # Users can only see their own current year expenses
        expenses = MonthlyExpense.objects.filter(user=request.user, year=current_year).order_by("month")

    serializer = MonthlyExpenseSummarySerializer(expenses, many=True)
    return StandardSuccessResponse.data_retrieved(
        message="Current year expenses retrieved successfully",
        details=f"Retrieved {len(serializer.data)} expense records for {current_year}",
        data=serializer.data
    )


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can view their expense statistics
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def expense_statistics(request):
    """
    Get expense statistics for the authenticated user.
    Business Rule: Users can only see their own stats, admins see global stats.
    """
    if request.user.role == 'admin':
        # Admins can see global statistics
        queryset = MonthlyExpense.objects.all()
    else:
        # Users can only see their own statistics
        queryset = MonthlyExpense.objects.filter(user=request.user)

    # Calculate total expenses
    total_expenses = queryset.aggregate(total=Sum("total_amount"))["total"] or 0

    # Get current year expenses
    current_year = timezone.now().year
    current_year_expenses = (
        queryset.filter(year=current_year).aggregate(total=Sum("total_amount"))["total"] or 0
    )

    # Get current month expenses
    current_month = timezone.now().month
    current_month_expenses = (
        queryset.filter(year=current_year, month=current_month).aggregate(
            total=Sum("total_amount")
        )["total"] or 0
    )

    return StandardSuccessResponse.data_retrieved(
        message="Expense statistics retrieved successfully",
        details="Current expense statistics and summaries",
        data={
            "total_all_time": total_expenses,
            "total_current_year": current_year_expenses,
            "total_current_month": current_month_expenses,
        }
    )


@api_view(["POST"])
@permission_classes([AnyRolePermission])  # Any authenticated user can buy tokens
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def buy_tokens(request):
    """
    Expected request data format:
    {
        "amount": "100.00",
        "payment_method": "CARD" | "MTM",
        "payment_details": {
            // For CARD:
            "card_number": "****************",
            "exp_month": "12",
            "exp_year": "2024",
            "cvc": "123"

            // For MTM:
            "mobile_number": "+254712345678"
        }
    }
    """
    try:
        # Validate request data
        amount = request.data.get("amount")
        payment_method = request.data.get("payment_method")
        payment_details = request.data.get("payment_details", {})

        if not all([amount, payment_method, payment_details]):
            raise_validation_error(
                message="Missing required fields",
                details="Amount, payment method, and payment details are all required"
            )

        # Validate amount
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        try:
            if amount <= 0:
                raise ValueError
        except (ValueError, TypeError):
            raise_validation_error(
                message="Invalid amount",
                details="Amount must be a positive number greater than zero"
            )

        # Validate payment method specific details
        if payment_method == "CARD":
            if "mobile_number" in payment_details:
                raise_validation_error(
                    message="Mobile number should not be included for card payments",
                    details="Card payments should only include card-related fields"
                )

            required_fields = ["card_number", "exp_month", "exp_year", "cvc"]
            if not all(field in payment_details for field in required_fields):
                raise_validation_error(
                    message="Missing required card details",
                    details=f"Required fields: {', '.join(required_fields)}"
                )

        elif payment_method == "MTM":
            if any(
                field in payment_details
                for field in ["card_number", "exp_month", "exp_year", "cvc"]
            ):
                raise_validation_error(
                    message="Card details should not be included for MTM payments",
                    details="MTM payments should only include mobile number"
                )

            if "mobile_number" not in payment_details:
                raise_validation_error(
                    message="Mobile number is required for MTM payments",
                    details="Mobile number field is required for mobile money transfers"
                )
        else:
            raise_validation_error(
                message="Invalid payment method",
                details="Payment method must be either 'CARD' or 'MTM'"
            )

        # Process payment based on method
        if payment_method == "CARD":
            result = process_card_payment(request.user, amount, payment_details)
        else:  # MTM
            result = process_mtm_payment(request.user, amount, payment_details)

        if not result["success"]:
            raise_business_logic_error(
                message=result["error"],
                details="Payment processing failed"
            )

        result1 = app_client.call(
            mint_tokens,
            amount=amount,
            receiver=request.user.account_address,
            token_id=ALGORAND_ASSET_ID_KTT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT],
            accounts=[request.user.account_address],
        )

        if result["success"]:
            # Create transaction record
            transaction = Transaction.objects.create(
                transaction_id=f"transaction_{int(time.time())}",
                algorand_tx_id=result1.tx_id,
                user=request.user,
                from_address=result["from_address"],
                to_address=request.user.account_address,
                amount_usd=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                amount_stablecoin_ktt=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                transaction_type="MINT",
                status="COMPLETED",
                gas_fee=result["gas_fee"],
                payment_method=payment_method,
            )

            serializer = TransactionSerializer(transaction)
            return StandardSuccessResponse.transaction_completed(
                message="Token purchase successful",
                details=f"Successfully purchased {Decimal(amount / 10 ** int(ASA_DECIMALS))} KTT tokens",
                transaction_data={
                    "transaction": serializer.data,
                    "additional_info": result.get("message", ""),
                    "payment_method": payment_method
                }
            )
        else:
            raise_business_logic_error(
                message=result["error"],
                details="Payment processing failed after token minting"
            )

    except Exception as e:
        print(e)
        return handle_exception_with_logging(e, "token purchase")


def process_card_payment(user, amount, payment_details):
    """
    Process card payment using Stripe
    """
    return {
        "success": True,
        "transaction_id": f"stripe_{int(time.time())}",
        "from_address": "STRIPE_PAYMENT",
        "gas_fee": Decimal("0.001"),
    }


def process_mtm_payment(user, amount, payment_details):
    """
    Process MTM (Mobile Money Transfer) payment
    """
    return {
        "success": True,
        "transaction_id": f"mtm_{int(time.time())}",
        "from_address": "MTM_PAYMENT",
        "gas_fee": Decimal("0.001"),
        "status": "pending",
        "message": "Please check your phone and complete the payment",
    }


@api_view(["POST", "GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can burn tokens
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def burn_token(request):
    if request.method == "GET":
        # Get the current user
        user = request.user

        # Filter transactions for the user with transaction type 'BURN'
        burn_transactions = Transaction.objects.filter(
            user=user, transaction_type="BURN"
        )

        # Calculate the total amount_stablecoin_ktt
        total_amount_stablecoin_ktt = (
            burn_transactions.aggregate(total=models.Sum("amount_stablecoin_ktt"))[
                "total"
            ]
            or 0
        )

        # Prepare the response data
        response_data = {
            "total_amount_stablecoin_ktt": str(
                total_amount_stablecoin_ktt
            ),  # Convert to string for JSON serialization
            "transaction_count": burn_transactions.count(),
        }

        return StandardSuccessResponse.data_retrieved(
            message="Burn transaction summary retrieved successfully",
            details=f"Retrieved summary of {burn_transactions.count()} burn transactions",
            data=response_data
        )
    else:
        try:
            amount = request.data.get("amount")
            amount = Decimal(amount)
            amount = int(amount * 10 ** int(ASA_DECIMALS))
            try:
                if amount <= 0:
                    raise ValueError
            except (ValueError, TypeError):
                raise_validation_error(
                    message="Invalid amount",
                    details="Amount must be a positive number greater than zero"
                )

            result1 = app_client.call(
                burn_tokens,
                amount=amount,
                address=request.user.account_address,
                token_id=ALGORAND_ASSET_ID_KTT,
                foreign_assets=[ALGORAND_ASSET_ID_KTT],
                accounts=[request.user.account_address],
            )

            # Create transaction record
            transaction = Transaction.objects.create(
                transaction_id=f"transaction_{int(time.time())}",
                algorand_tx_id=result1.tx_id,
                user=request.user,
                from_address=request.user.account_address,
                to_address="",
                amount_usd=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                amount_stablecoin_ktt=Decimal(amount / 10 ** int(ASA_DECIMALS)),
                transaction_type="BURN",
                status="COMPLETED",
                gas_fee="0.001",
                payment_method="",
            )

            serializer = TransactionSerializer(transaction)
            return StandardSuccessResponse.transaction_completed(
                message="Token burn successful",
                details=f"Successfully burned {Decimal(amount / 10 ** int(ASA_DECIMALS))} KTT tokens",
                transaction_data={
                    "transaction": serializer.data,
                    "additional_info": "Tokens have been permanently removed from circulation"
                }
            )
        except Exception as e:
            print(e)
            return handle_exception_with_logging(e, "token burning")


@api_view(["POST"])
@permission_classes([AnyRolePermission])  # Any authenticated user can convert tokens
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def convert_ktt_to_kct(request):
    """
    Convert KTT tokens to KCT tokens.
    Business Rule: Any authenticated user can convert their tokens.
    """
    try:
        amount = request.data.get("amount")
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        result = app_client.call(
            convert_ktt_tokens_to_kct,
            amount=amount,
            address=request.user.account_address,
            token_ktt_id=ALGORAND_ASSET_ID_KTT,
            token_kct_id=ALGORAND_ASSET_ID_KCT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT, ALGORAND_ASSET_ID_KCT],
            accounts=[request.user.account_address],
        )
        # Create transaction record
        transaction = Transaction.objects.create(
            transaction_id=f"transaction_{int(time.time())}",
            algorand_tx_id=result.tx_id,
            user=request.user,
            from_address=request.user.account_address,
            to_address=request.user.account_address,
            amount_usd=0,
            amount_stablecoin_ktt=-Decimal(amount / 10 ** int(ASA_DECIMALS)),
            amount_stablecoin_kct=Decimal(amount / 10 ** int(ASA_DECIMALS)),
            transaction_type="CONVERT_KTT_TO_KCT",
            status="COMPLETED",
            gas_fee="0.001",
            payment_method="",
        )

        serializer = TransactionSerializer(transaction)
        return StandardSuccessResponse.transaction_completed(
            message="Token conversion successful",
            details=f"Successfully converted {Decimal(amount / 10 ** int(ASA_DECIMALS))} KTT to KCT tokens",
            transaction_data={
                "transaction": serializer.data,
                "conversion_type": "KTT_TO_KCT",
                "additional_info": "Tokens have been converted and are now available as KCT"
            }
        )
    except Exception as e:
        return handle_exception_with_logging(e, "KTT to KCT conversion")


@api_view(["POST"])
@permission_classes([AnyRolePermission])  # Any authenticated user can convert tokens
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def convert_kct_to_ktt(request):
    try:
        amount = request.data.get("amount")
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        result = app_client.call(
            convert_kct_tokens_to_ktt,
            amount=amount,
            address=request.user.account_address,
            token_ktt_id=ALGORAND_ASSET_ID_KTT,
            token_kct_id=ALGORAND_ASSET_ID_KCT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT, ALGORAND_ASSET_ID_KCT],
            accounts=[request.user.account_address],
        )
        # Create transaction record
        transaction = Transaction.objects.create(
            transaction_id=f"transaction_{int(time.time())}",
            algorand_tx_id=result.tx_id,
            user=request.user,
            from_address=request.user.account_address,
            to_address=request.user.account_address,
            amount_usd=0,
            amount_stablecoin_ktt=-Decimal(amount / 10 ** int(ASA_DECIMALS)),
            amount_stablecoin_kct=Decimal(amount / 10 ** int(ASA_DECIMALS)),
            transaction_type="CONVERT_KCT_TO_KTT",
            status="COMPLETED",
            gas_fee="0.001",
            payment_method="",
        )

        serializer = TransactionSerializer(transaction)
        return StandardSuccessResponse.transaction_completed(
            message="Token conversion successful",
            details=f"Successfully converted {Decimal(amount / 10 ** int(ASA_DECIMALS))} KCT to KTT tokens",
            transaction_data={
                "transaction": serializer.data,
                "conversion_type": "KCT_TO_KTT",
                "additional_info": "Tokens have been converted and are now available as KTT"
            }
        )
    except Exception as e:
        return handle_exception_with_logging(e, "KCT to KTT conversion")


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Any authenticated user can view their transactions
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def get_all_transactions(request):
    """
    Get all transactions for the authenticated user (sent and received).
    Business Rule: Users can only see their own transactions, admins can see all.
    """
    try:
        if request.user.role == 'admin':
            # Admins can see all transactions
            sent_transactions = Transaction.objects.all()
            # For admins, also get all received transactions
            sql_query = """
                SELECT * FROM transactions_transaction
                WHERE transaction_type='TRANSFER'
            """
            received_transactions = Transaction.objects.raw(sql_query)
        else:
            # Users can only see their own transactions
            sent_transactions = Transaction.objects.filter(user=request.user)
            # Get received transactions for this user
            sql_query = """
                SELECT * FROM transactions_transaction
                WHERE to_address = %s AND transaction_type='TRANSFER'
            """
            received_transactions = Transaction.objects.raw(
                sql_query, [request.user.account_address]
            )

        sent_serializer = TransactionSerializer(sent_transactions, many=True)
        all_transactions = list(sent_serializer.data)

        # Add received transactions to the list
        for transaction in received_transactions:
            transaction_data = TransactionSerializer(transaction).data
            transaction_data["transaction_type"] = "RECEIVE"
            transaction_data["amount_stablecoin_ktt"] = -Decimal(
                transaction_data["amount_stablecoin_ktt"]
            )
            all_transactions.append(transaction_data)

        return StandardSuccessResponse.data_retrieved(
            message="All transactions retrieved successfully",
            details=f"Retrieved {len(all_transactions)} transaction records (sent and received)",
            data=all_transactions
        )
    except Exception as e:
        print(e)
        return handle_exception_with_logging(e, "transaction retrieval")


@api_view(["POST"])
@permission_classes([AnyRolePermission])  # Any authenticated user can transfer tokens
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def transfer_ktt(request):
    try:
        amount = request.data.get("amount")
        reciver_address = str(request.data.get("reciver"))
        amount = Decimal(amount)
        amount = int(amount * 10 ** int(ASA_DECIMALS))
        result = app_client.call(
            transfer_tokens,
            amount=amount,
            receiver=reciver_address,
            account=request.user.account_address,
            token_id=ALGORAND_ASSET_ID_KTT,
            foreign_assets=[ALGORAND_ASSET_ID_KTT],
            accounts=[request.user.account_address, reciver_address],
        )
        # Create transaction record
        transaction = Transaction.objects.create(
            transaction_id=f"transaction_{int(time.time())}",
            algorand_tx_id=result.tx_id,
            user=request.user,
            from_address=request.user.account_address,
            to_address=reciver_address,
            amount_usd=0,
            amount_stablecoin_ktt=-Decimal(amount / 10 ** int(ASA_DECIMALS)),
            amount_stablecoin_kct=0,
            transaction_type="TRANSFER",
            status="COMPLETED",
            gas_fee="0.001",
            payment_method="",
        )

        serializer = TransactionSerializer(transaction)
        return StandardSuccessResponse.transaction_completed(
            message="Token transfer successful",
            details=f"Successfully transferred {Decimal(amount / 10 ** int(ASA_DECIMALS))} KTT tokens to {reciver_address}",
            transaction_data={
                "transaction": serializer.data,
                "recipient_address": reciver_address,
                "additional_info": "Transfer has been completed and recorded on the blockchain"
            }
        )
    except Exception as e:
        print(e)
        return handle_exception_with_logging(e, "KTT transfer")


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def get_monthly_expenses(request):
    monthly = MonthlyExpense.objects.filter(user=request.user, transaction_type="MINT")
    serializer = MonthlyExpenseSerializer(monthly, many=True)

    if not monthly.exists():
        raise_not_found_error(
            message="No monthly expenses found",
            details="No monthly expense records found for the current user",
            resource_type="monthly_expenses"
        )

    return StandardSuccessResponse.data_retrieved(
        message="Monthly expenses retrieved successfully",
        details=f"Retrieved {len(serializer.data)} monthly expense records for minting transactions",
        data=serializer.data
    )
