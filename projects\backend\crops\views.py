from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from oauth2_provider.contrib.rest_framework import OAuth2Authentication
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from .models import CropTransfer, Crops
from .serializers import (
    CropTransferSerializer,
    CropsSerializer,
    CropTransferHistorySerializer,
)
from django.db import models
from oauth2_auth.permissions import AnyRolePermission
from rest_framework import permissions
from user.permissions import UserStatusMixin
from agritram.message_utils import handle_serializer_errors, handle_exception_with_logging, StandardSuccessResponse
import logging

logger = logging.getLogger(__name__)


class CropViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Custom permission for crop operations based on business rules.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"Crop view permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # Only farmers, traders, and manufacturers can view crops
        return user_role in ['farmer', 'trader', 'manufacturer', 'admin']


class CropCreatePermission(permissions.BasePermission):
    """
    Permission for creating crops.
    Business Rule: Traders can create crops they receive from farmers.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        user_role = request.user.role

        # Only traders and admins can create crops
        return user_role in ['trader', 'admin']


class CropTransferPermission(permissions.BasePermission):
    """
    Permission for creating crop transfers.
    Business Rule: Traders and manufacturers can create crop transfers.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        user_role = request.user.role

        # Only traders, manufacturers, and admins can create crop transfers
        return user_role in ['trader', 'manufacturer', 'admin']


@api_view(["POST"])
@permission_classes([CropTransferPermission])  # Traders and manufacturers can create crop transfers
@authentication_classes([OAuth2Authentication, SessionAuthentication, TokenAuthentication])
def create_crop_transfer(request):
    """
    Create a new crop transfer record.
    Business Rule: Traders and manufacturers can create crop transfers.
    """
    print(request.data)
    try:
        serializer = CropTransferSerializer(data=request.data)
        if serializer.is_valid():
            transfer = serializer.save()
            return StandardSuccessResponse.record_created(
                message="Crop transfer created successfully",
                details=f"New crop transfer record created with ID: {transfer.id}",
                record_data=CropTransferSerializer(transfer).data
            )
        else:
            print(serializer.errors)
            return handle_serializer_errors(serializer)
    except Exception as e:
        print(e)
        return handle_exception_with_logging(e, "crop transfer creation")


@api_view(["GET"])
@permission_classes([CropViewPermission])  # Farmers, traders, and manufacturers can view crops
@authentication_classes([OAuth2Authentication, SessionAuthentication, TokenAuthentication])
def get_crop(request, crop_id):
    """
    Retrieve a crop by its crop_id.
    Business Rule: Only farmers, traders, and manufacturers can view crops.
    """
    try:
        crop = get_object_or_404(Crops, crop_id=crop_id)
        serializer = CropsSerializer(crop)
        return StandardSuccessResponse.data_retrieved(
            message="Crop retrieved successfully",
            details=f"Retrieved crop information for crop ID: {crop_id}",
            data=serializer.data
        )
    except Exception as e:
        return handle_exception_with_logging(e, "crop retrieval")


@api_view(["GET"])
@permission_classes([CropViewPermission])  # Farmers, traders, and manufacturers can view crops
@authentication_classes([OAuth2Authentication, SessionAuthentication, TokenAuthentication])
def get_all_crops(request):
    """
    Retrieve all crops.
    Business Rule: Only farmers, traders, and manufacturers can view crops.
    """
    try:
        crops = Crops.objects.all()
        serializer = CropsSerializer(crops, many=True)
        return StandardSuccessResponse.data_retrieved(
            message="All crops retrieved successfully",
            details=f"Retrieved {len(serializer.data)} crop records",
            data=serializer.data
        )
    except Exception as e:
        return handle_exception_with_logging(e, "crops retrieval")


@api_view(["GET"])
@permission_classes([AnyRolePermission])  # Anyone authenticated can view crop transfer history
@authentication_classes([OAuth2Authentication, SessionAuthentication, TokenAuthentication])
def get_crop_transaction_history(request, crop_id):
    """
    Retrieve transaction history for a specific crop.
    Business Rule: Anyone authenticated can view crop transfer history.
    """
    try:
        transfers = (
            CropTransfer.objects.filter(crop__crop_id=crop_id)
            .order_by("timestamp")
        )
        serializer = CropTransferHistorySerializer(transfers, many=True)
        return StandardSuccessResponse.data_retrieved(
            message="Crop transaction history retrieved successfully",
            details=f"Retrieved {len(serializer.data)} transaction records for crop ID: {crop_id}",
            data=serializer.data
        )
    except Exception as e:
        return handle_exception_with_logging(e, "crop transaction history retrieval")
