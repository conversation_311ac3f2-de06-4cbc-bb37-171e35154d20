from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from .managers import UserManager

class RoleChoices(models.TextChoices):
    FARMER = 'farmer', 'Farmer'
    TRADER = 'trader', 'Trader'
    MANUFACTURER = 'manufacturer', 'Manufacturer'
    ADMIN = 'admin', 'Admin'

class User(AbstractUser):
    username = None
    first_name = None
    last_name = None

    # Basic info
    name = models.CharField(max_length=255)
    email = models.EmailField(unique=True)

    # Permissions
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_mail_verified = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    # Timestamps
    date_joined = models.DateTimeField(default=timezone.now, db_column="account_creation_date")
    last_login = models.DateTimeField(null=True, blank=True)
    password = models.Char<PERSON>ield(max_length=255)
    role = models.Char<PERSON>ield(
        max_length=50,
        default=RoleChoices.FARMER,
        choices=RoleChoices.choices,
    )
    account_address = models.CharField(max_length=255, null=True, blank=True)
    opt_in = models.BooleanField(default=False)

    # Account lockout fields
    failed_login_attempts = models.PositiveIntegerField(default=0)
    lockout_until = models.DateTimeField(null=True, blank=True)
    lockout_count = models.PositiveIntegerField(default=0)  # Number of times account has been locked
    is_permanently_locked = models.BooleanField(default=False)
    last_failed_login = models.DateTimeField(null=True, blank=True)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    objects = UserManager()

    class Meta:
        verbose_name = "User"
        verbose_name_plural = "Users"
        indexes = [models.Index(fields=['email']),]

    def __str__(self):
        return self.email

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.is_permanently_locked:
            return True
        if self.lockout_until and timezone.now() < self.lockout_until:
            return True
        return False

    def get_lockout_remaining_time(self):
        """Get remaining lockout time in seconds"""
        if self.is_permanently_locked:
            return None  # Permanent lockout
        if self.lockout_until and timezone.now() < self.lockout_until:
            return (self.lockout_until - timezone.now()).total_seconds()
        return 0

    def reset_failed_attempts(self):
        """Reset failed login attempts after successful login"""
        self.failed_login_attempts = 0
        self.last_failed_login = None
        self.save(update_fields=['failed_login_attempts', 'last_failed_login'])

    def unlock_account(self):
        """Unlock account (admin action)"""
        self.failed_login_attempts = 0
        self.lockout_until = None
        self.is_permanently_locked = False
        self.last_failed_login = None
        self.save(update_fields=[
            'failed_login_attempts', 'lockout_until',
            'is_permanently_locked', 'last_failed_login'
        ])
