"""
Custom exception handlers for Django REST Framework.

This module provides a centralized exception handler that formats all API errors
according to the standardized error response format.
"""

import logging
from django.conf import settings
from django.core.exceptions import ValidationError as DjangoValidationError
from django.http import Http404
from django.db import IntegrityError
from rest_framework import status
from rest_framework.views import exception_handler as drf_exception_handler
from rest_framework.response import Response
from rest_framework.exceptions import (
    ValidationError,
    AuthenticationFailed,
    PermissionDenied,
    NotFound,
    MethodNotAllowed,
    NotAcceptable,
    UnsupportedMediaType,
    Throttled,
    ParseError,
    APIException
)

from .exceptions import AgritramAPIException
from .message_utils import StandardErrorResponse, handle_exception_with_logging

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that formats all exceptions using StandardErrorResponse.
    
    This handler catches all exceptions and formats them according to our
    standardized error response format.
    
    Args:
        exc: The exception instance
        context: Context information about the exception
        
    Returns:
        Response: Standardized error response
    """
    
    # Get the view and request from context
    view = context.get('view')
    request = context.get('request')
    
    # Log the exception for debugging
    if view:
        view_name = f"{view.__class__.__module__}.{view.__class__.__name__}"
    else:
        view_name = "Unknown"
    
    logger.warning(f"Exception in {view_name}: {exc.__class__.__name__}: {str(exc)}")
    
    # Handle our custom exceptions first
    if isinstance(exc, AgritramAPIException):
        return StandardErrorResponse.create_error_response(
            code=exc.code,
            message=exc.message,
            details=exc.details,
            actions=exc.actions,
            status_code=exc.status_code,
            extra_data=exc.extra_data
        )
    
    # Handle DRF built-in exceptions
    if isinstance(exc, ValidationError):
        # Handle DRF validation errors
        field_errors = {}
        non_field_errors = []
        
        if hasattr(exc, 'detail') and isinstance(exc.detail, dict):
            for field, errors in exc.detail.items():
                if field == 'non_field_errors':
                    non_field_errors.extend(errors if isinstance(errors, list) else [errors])
                else:
                    field_errors[field] = errors if isinstance(errors, list) else [errors]
        elif hasattr(exc, 'detail') and isinstance(exc.detail, list):
            non_field_errors.extend(exc.detail)
        else:
            non_field_errors.append(str(exc))
        
        # Create main error message
        if non_field_errors:
            message = str(non_field_errors[0])
            details = "; ".join(str(error) for error in non_field_errors) if len(non_field_errors) > 1 else None
        elif field_errors:
            # Use first field error as main message
            first_field = next(iter(field_errors))
            first_error = field_errors[first_field]
            message = f"{first_field}: {first_error[0] if isinstance(first_error, list) else first_error}"
            details = "Multiple validation errors occurred"
        else:
            message = "Validation failed"
            details = None
        
        return StandardErrorResponse.validation_error(
            message=message,
            details=details,
            field_errors=field_errors if field_errors else None
        )
    
    elif isinstance(exc, AuthenticationFailed):
        return StandardErrorResponse.authentication_error(
            message=str(exc),
            details="Authentication credentials were not provided or are invalid."
        )
    
    elif isinstance(exc, PermissionDenied):
        return StandardErrorResponse.authorization_error(
            message=str(exc),
            details="You do not have permission to perform this action."
        )
    
    elif isinstance(exc, (NotFound, Http404)):
        return StandardErrorResponse.not_found_error(
            message=str(exc) if str(exc) != "Not found." else "Resource not found",
            details="The requested resource could not be found."
        )
    
    elif isinstance(exc, MethodNotAllowed):
        allowed_methods = getattr(exc, 'detail', {}).get('allowed_methods', [])
        details = f"Allowed methods: {', '.join(allowed_methods)}" if allowed_methods else None
        return StandardErrorResponse.create_error_response(
            code="METHOD_NOT_ALLOWED",
            message="Method not allowed",
            details=details,
            status_code=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    elif isinstance(exc, NotAcceptable):
        return StandardErrorResponse.create_error_response(
            code="NOT_ACCEPTABLE",
            message="Not acceptable",
            details=str(exc),
            status_code=status.HTTP_406_NOT_ACCEPTABLE
        )
    
    elif isinstance(exc, UnsupportedMediaType):
        return StandardErrorResponse.create_error_response(
            code="UNSUPPORTED_MEDIA_TYPE",
            message="Unsupported media type",
            details=str(exc),
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE
        )
    
    elif isinstance(exc, Throttled):
        # Django REST Framework throttling disabled - rate limiting handled by rate_limiting_service.py
        # This should not occur as DRF throttling is disabled, but keeping for compatibility
        retry_after = getattr(exc, 'wait', None)
        return StandardErrorResponse.rate_limit_error(
            message="Rate limit exceeded (legacy DRF throttling)",
            details="Please use the enhanced rate limiting service instead",
            retry_after=int(retry_after) if retry_after else None
        )
    
    elif isinstance(exc, ParseError):
        return StandardErrorResponse.create_error_response(
            code="PARSE_ERROR",
            message="Parse error",
            details=str(exc),
            actions="Please check your request format and try again.",
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    # Handle Django core exceptions
    elif isinstance(exc, DjangoValidationError):
        if hasattr(exc, 'message_dict'):
            # Field-specific validation errors
            field_errors = exc.message_dict
            first_field = next(iter(field_errors))
            first_error = field_errors[first_field]
            message = f"{first_field}: {first_error[0] if isinstance(first_error, list) else first_error}"
            
            return StandardErrorResponse.validation_error(
                message=message,
                details="Multiple validation errors occurred",
                field_errors=field_errors
            )
        else:
            # Non-field validation errors
            messages = exc.messages if hasattr(exc, 'messages') else [str(exc)]
            return StandardErrorResponse.validation_error(
                message=messages[0],
                details="; ".join(messages) if len(messages) > 1 else None
            )
    
    elif isinstance(exc, IntegrityError):
        # Database integrity constraint violations
        return StandardErrorResponse.create_error_response(
            code="INTEGRITY_ERROR",
            message="Data integrity constraint violation",
            details="The operation violates a database constraint" if not settings.DEBUG else str(exc),
            actions="Please check your data and try again.",
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    # Handle other DRF API exceptions
    elif isinstance(exc, APIException):
        return StandardErrorResponse.create_error_response(
            code=getattr(exc, 'default_code', 'API_ERROR').upper(),
            message=str(exc),
            details=getattr(exc, 'detail', None),
            status_code=getattr(exc, 'status_code', status.HTTP_400_BAD_REQUEST)
        )
    
    # Handle any other unexpected exceptions
    else:
        # Log the full exception for debugging
        logger.error(f"Unhandled exception in {view_name}: {exc.__class__.__name__}: {str(exc)}", exc_info=True)
        
        # Return a generic server error
        if settings.DEBUG:
            details = f"{exc.__class__.__name__}: {str(exc)}"
        else:
            details = "An unexpected error occurred"
        
        return StandardErrorResponse.server_error(
            message="Internal server error",
            details=details
        )


def handle_404_error(request, exception=None):
    """
    Custom 404 handler that returns standardized error format.
    """
    return StandardErrorResponse.not_found_error(
        message="Page not found",
        details=f"The requested URL '{request.path}' was not found on this server."
    )


def handle_500_error(request):
    """
    Custom 500 handler that returns standardized error format.
    """
    logger.error(f"500 error for request: {request.path}", exc_info=True)
    
    return StandardErrorResponse.server_error(
        message="Internal server error",
        details="An unexpected error occurred" if not settings.DEBUG else "Check server logs for details"
    )
