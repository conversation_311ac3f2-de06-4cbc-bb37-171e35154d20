from rest_framework import serializers
from django.contrib.auth import get_user_model
from oauth2_provider.models import Application, AccessToken, RefreshToken
from .models import DeviceToken, SecurityEvent

User = get_user_model()


class OAuth2ApplicationSerializer(serializers.ModelSerializer):
    """
    Serializer for OAuth2 Application model
    """
    client_secret = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = Application
        fields = [
            'id', 'name', 'client_id', 'client_secret', 'client_type',
            'authorization_grant_type', 'redirect_uris', 'skip_authorization',
            'created', 'updated'
        ]
        read_only_fields = ['id', 'client_id', 'created', 'updated']
        extra_kwargs = {
            'client_secret': {'write_only': True}
        }


class OAuth2AccessTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for OAuth2 Access Token model
    """
    user_email = serializers.EmailField(source='user.email', read_only=True)
    application_name = serializers.<PERSON><PERSON><PERSON><PERSON>(source='application.name', read_only=True)
    is_valid = serializers.SerializerMethodField()

    class Meta:
        model = AccessToken
        fields = [
            'id', 'token', 'user', 'user_email', 'application', 'application_name',
            'scope', 'expires', 'created', 'updated', 'is_valid'
        ]
        read_only_fields = [
            'id', 'token', 'created', 'updated', 'user_email', 'application_name', 'is_valid'
        ]

    def get_is_valid(self, obj):
        return obj.is_valid()


class OAuth2RefreshTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for OAuth2 Refresh Token model
    """
    user_email = serializers.EmailField(source='user.email', read_only=True)
    application_name = serializers.CharField(source='application.name', read_only=True)
    is_valid = serializers.SerializerMethodField()

    class Meta:
        model = RefreshToken
        fields = [
            'id', 'token', 'user', 'user_email', 'application', 'application_name',
            'created', 'updated', 'is_valid'
        ]
        read_only_fields = [
            'id', 'token', 'created', 'updated', 'user_email', 'application_name', 'is_valid'
        ]

    def get_is_valid(self, obj):
        return obj.is_valid()


class DeviceTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for Device Token model
    """
    user_email = serializers.EmailField(source='user.email', read_only=True)
    is_trust_valid = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceToken
        fields = [
            'id', 'user', 'user_email', 'device_id', 'device_name', 'device_type',
            'is_trusted', 'trust_expires', 'fingerprint', 'first_seen', 'last_seen',
            'ip_address', 'user_agent', 'is_blocked', 'blocked_at', 'blocked_reason',
            'is_trust_valid'
        ]
        read_only_fields = [
            'id', 'device_id', 'fingerprint', 'first_seen', 'last_seen',
            'user_email', 'is_trust_valid'
        ]
    
    def get_is_trust_valid(self, obj):
        return obj.is_trust_valid()


class SecurityEventSerializer(serializers.ModelSerializer):
    """
    Serializer for Security Event model
    """
    user_email = serializers.EmailField(source='user.email', read_only=True)
    application_name = serializers.CharField(source='application.name', read_only=True)
    event_type_display = serializers.CharField(source='get_event_type_display', read_only=True)
    
    class Meta:
        model = SecurityEvent
        fields = [
            'id', 'user', 'user_email', 'event_type', 'event_type_display',
            'description', 'ip_address', 'user_agent', 'device_id',
            'application', 'application_name', 'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'user_email', 'application_name', 'event_type_display', 'created_at']


class TokenRequestSerializer(serializers.Serializer):
    """
    Serializer for OAuth2 token requests
    """
    grant_type = serializers.ChoiceField(choices=[
        'authorization_code',
        'refresh_token',
        'password',
        'client_credentials'
    ])
    client_id = serializers.CharField()
    client_secret = serializers.CharField(required=False)
    code = serializers.CharField(required=False)
    redirect_uri = serializers.URLField(required=False)
    refresh_token = serializers.CharField(required=False)
    username = serializers.CharField(required=False)
    password = serializers.CharField(required=False)
    scope = serializers.CharField(required=False)
    code_verifier = serializers.CharField(required=False)
    device_id = serializers.CharField(required=False)
    
    def validate(self, attrs):
        grant_type = attrs.get('grant_type')
        
        if grant_type == 'authorization_code':
            if not attrs.get('code'):
                raise serializers.ValidationError("Authorization code is required")
            if not attrs.get('redirect_uri'):
                raise serializers.ValidationError("Redirect URI is required")
        
        elif grant_type == 'refresh_token':
            if not attrs.get('refresh_token'):
                raise serializers.ValidationError("Refresh token is required")
        
        elif grant_type == 'password':
            if not attrs.get('username') or not attrs.get('password'):
                raise serializers.ValidationError("Username and password are required")
        
        return attrs


class TokenIntrospectionSerializer(serializers.Serializer):
    """
    Serializer for OAuth2 token introspection requests
    """
    token = serializers.CharField()
    token_type_hint = serializers.ChoiceField(
        choices=['access_token', 'refresh_token'],
        required=False
    )
    client_id = serializers.CharField()
    client_secret = serializers.CharField()


class TokenRevocationSerializer(serializers.Serializer):
    """
    Serializer for OAuth2 token revocation requests
    """
    token = serializers.CharField()
    token_type_hint = serializers.ChoiceField(
        choices=['access_token', 'refresh_token'],
        required=False
    )
    client_id = serializers.CharField()
    client_secret = serializers.CharField()


class UserInfoSerializer(serializers.ModelSerializer):
    """
    Serializer for OpenID Connect UserInfo endpoint
    """
    sub = serializers.CharField(source='id', read_only=True)
    
    class Meta:
        model = User
        fields = ['sub', 'email', 'name', 'role', 'account_address', 'opt_in']
        read_only_fields = ['sub', 'email', 'name', 'role', 'account_address', 'opt_in']


class JWTTokenSerializer(serializers.Serializer):
    """
    Serializer for JWT token generation
    """
    user_id = serializers.IntegerField()
    client_id = serializers.CharField()
    scope = serializers.CharField(required=False)
    expires_in = serializers.IntegerField(default=3600)
    
    def validate_user_id(self, value):
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("User does not exist")
        return value
    
    def validate_client_id(self, value):
        try:
            Application.objects.get(client_id=value)
        except Application.DoesNotExist:
            raise serializers.ValidationError("Application does not exist")
        return value


class DeviceRegistrationSerializer(serializers.Serializer):
    """
    Serializer for device registration
    """
    device_id = serializers.CharField()
    device_name = serializers.CharField()
    device_type = serializers.ChoiceField(choices=[
        ('web', 'Web Browser'),
        ('mobile', 'Mobile App'),
        ('desktop', 'Desktop App'),
        ('api', 'API Client'),
    ])
    fingerprint = serializers.CharField(required=False)
    user_agent = serializers.CharField(required=False)
    
    def validate_device_id(self, value):
        # Ensure device_id is unique for the user
        user = self.context.get('user')
        if user and DeviceToken.objects.filter(user=user, device_id=value).exists():
            raise serializers.ValidationError("Device already registered")
        return value
