"""
Token Invalidation Service

Provides immediate token invalidation capabilities for security incidents,
compromised tokens, and emergency security responses.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from django.core.cache import cache
from oauth2_provider.models import AccessToken, RefreshToken
from .models import SecureToken, DeviceToken, SecurityEvent
from .utils import log_security_event, get_client_ip

User = get_user_model()
logger = logging.getLogger(__name__)


class TokenInvalidationService:
    """
    Service for immediate token invalidation and security incident response
    """
    
    # Token blacklist cache settings
    BLACKLIST_CACHE_PREFIX = "token_blacklist_"
    BLACKLIST_CACHE_TIMEOUT = 86400 * 7  # 7 days
    
    # Invalidation reasons
    SECURITY_BREACH = "security_breach"
    COMPROMISED_DEVICE = "compromised_device"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    ADMIN_ACTION = "admin_action"
    USER_REQUEST = "user_request"
    ACCOUNT_LOCKOUT = "account_lockout"
    PASSWORD_CHANGE = "password_change"
    
    @classmethod
    def invalidate_user_tokens(cls, user, reason: str, admin_user=None, request=None) -> Dict[str, Any]:
        """
        Invalidate all tokens for a specific user
        
        Args:
            user: User instance
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)
            
        Returns:
            dict: Invalidation results
        """
        try:
            with transaction.atomic():
                results = {
                    'oauth2_access_tokens': 0,
                    'oauth2_refresh_tokens': 0,
                    'secure_tokens': 0,
                    'device_tokens': 0,
                    'total_invalidated': 0
                }
                
                # Invalidate OAuth2 access tokens
                access_tokens = AccessToken.objects.filter(user=user)
                for token in access_tokens:
                    cls._blacklist_token(token.token, reason)
                access_token_count = access_tokens.count()
                access_tokens.delete()
                results['oauth2_access_tokens'] = access_token_count
                
                # Invalidate OAuth2 refresh tokens
                refresh_tokens = RefreshToken.objects.filter(user=user)
                for token in refresh_tokens:
                    cls._blacklist_token(token.token, reason)
                refresh_token_count = refresh_tokens.count()
                refresh_tokens.delete()
                results['oauth2_refresh_tokens'] = refresh_token_count
                
                # Invalidate secure tokens
                secure_tokens = SecureToken.objects.filter(user=user, status='active')
                for token in secure_tokens:
                    token.invalidate(reason)
                results['secure_tokens'] = secure_tokens.count()
                
                # Invalidate device tokens
                device_tokens = DeviceToken.objects.filter(user=user, is_active=True)
                device_token_count = device_tokens.count()
                device_tokens.update(is_active=False, deactivated_at=timezone.now())
                results['device_tokens'] = device_token_count
                
                results['total_invalidated'] = sum(results.values()) - results['total_invalidated']
                
                # Log security event
                log_security_event(
                    user=user,
                    event_type='all_tokens_invalidated',
                    description=f'All user tokens invalidated: {reason}',
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                    metadata={
                        'reason': reason,
                        'admin_user': admin_user.email if admin_user else None,
                        'invalidation_results': results
                    }
                )
                
                logger.info(f"Invalidated all tokens for user {user.email}: {results}")
                return results
                
        except Exception as e:
            logger.error(f"Error invalidating user tokens: {str(e)}")
            return {'error': str(e)}
    
    @classmethod
    def invalidate_specific_token(cls, token_value: str, token_type: str, reason: str, 
                                 admin_user=None, request=None) -> bool:
        """
        Invalidate a specific token
        
        Args:
            token_value: Token value to invalidate
            token_type: Type of token ('access', 'refresh', 'secure', 'device')
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)
            
        Returns:
            bool: Success status
        """
        try:
            success = False
            user = None
            
            if token_type == 'access':
                try:
                    token = AccessToken.objects.get(token=token_value)
                    user = token.user
                    cls._blacklist_token(token_value, reason)
                    token.delete()
                    success = True
                except AccessToken.DoesNotExist:
                    pass
            
            elif token_type == 'refresh':
                try:
                    token = RefreshToken.objects.get(token=token_value)
                    user = token.user
                    cls._blacklist_token(token_value, reason)
                    token.delete()
                    success = True
                except RefreshToken.DoesNotExist:
                    pass
            
            elif token_type == 'secure':
                from .utils import hash_token
                token_hash = hash_token(token_value)
                try:
                    token = SecureToken.objects.get(token_hash=token_hash, status='active')
                    user = token.user
                    token.invalidate(reason)
                    success = True
                except SecureToken.DoesNotExist:
                    pass
            
            if success and user:
                log_security_event(
                    user=user,
                    event_type='specific_token_invalidated',
                    description=f'Specific {token_type} token invalidated: {reason}',
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                    metadata={
                        'token_type': token_type,
                        'reason': reason,
                        'admin_user': admin_user.email if admin_user else None
                    }
                )
                
                logger.info(f"Invalidated {token_type} token for user {user.email}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error invalidating specific token: {str(e)}")
            return False
    
    @classmethod
    def invalidate_device_tokens(cls, device_id: str, reason: str, admin_user=None, request=None) -> int:
        """
        Invalidate all tokens for a specific device
        
        Args:
            device_id: Device identifier
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)
            
        Returns:
            int: Number of tokens invalidated
        """
        try:
            with transaction.atomic():
                # Invalidate device tokens
                device_tokens = DeviceToken.objects.filter(device_id=device_id, is_active=True)
                count = device_tokens.count()
                
                users_affected = []
                for token in device_tokens:
                    users_affected.append(token.user)
                
                device_tokens.update(is_active=False, deactivated_at=timezone.now())
                
                # Invalidate OAuth2 tokens for this device (if we can identify them)
                # This is more complex as OAuth2 tokens don't directly link to devices
                # We'll log the event for manual review if needed
                
                # Log security events for affected users
                for user in set(users_affected):
                    log_security_event(
                        user=user,
                        event_type='device_tokens_invalidated',
                        description=f'Device tokens invalidated: {reason}',
                        ip_address=get_client_ip(request) if request else None,
                        user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                        device_id=device_id,
                        metadata={
                            'reason': reason,
                            'admin_user': admin_user.email if admin_user else None,
                            'tokens_invalidated': count
                        }
                    )
                
                logger.info(f"Invalidated {count} device tokens for device {device_id}")
                return count
                
        except Exception as e:
            logger.error(f"Error invalidating device tokens: {str(e)}")
            return 0
    
    @classmethod
    def is_token_blacklisted(cls, token_value: str) -> bool:
        """
        Check if a token is blacklisted
        
        Args:
            token_value: Token value to check
            
        Returns:
            bool: True if token is blacklisted
        """
        try:
            cache_key = f"{cls.BLACKLIST_CACHE_PREFIX}{hash(token_value)}"
            return cache.get(cache_key) is not None
        except Exception as e:
            logger.error(f"Error checking token blacklist: {str(e)}")
            return False
    
    @classmethod
    def emergency_invalidate_all(cls, reason: str, admin_user, request=None) -> Dict[str, Any]:
        """
        Emergency function to invalidate ALL tokens in the system
        
        Args:
            reason: Reason for emergency invalidation
            admin_user: Admin user performing the action
            request: HTTP request object (optional)
            
        Returns:
            dict: Invalidation results
        """
        try:
            with transaction.atomic():
                results = {
                    'oauth2_access_tokens': 0,
                    'oauth2_refresh_tokens': 0,
                    'secure_tokens': 0,
                    'device_tokens': 0,
                    'total_invalidated': 0
                }
                
                # Invalidate all OAuth2 access tokens
                access_tokens = AccessToken.objects.all()
                for token in access_tokens:
                    cls._blacklist_token(token.token, reason)
                results['oauth2_access_tokens'] = access_tokens.count()
                access_tokens.delete()
                
                # Invalidate all OAuth2 refresh tokens
                refresh_tokens = RefreshToken.objects.all()
                for token in refresh_tokens:
                    cls._blacklist_token(token.token, reason)
                results['oauth2_refresh_tokens'] = refresh_tokens.count()
                refresh_tokens.delete()
                
                # Invalidate all secure tokens
                secure_tokens = SecureToken.objects.filter(status='active')
                for token in secure_tokens:
                    token.invalidate(reason)
                results['secure_tokens'] = secure_tokens.count()
                
                # Invalidate all device tokens
                device_tokens = DeviceToken.objects.filter(is_active=True)
                results['device_tokens'] = device_tokens.count()
                device_tokens.update(is_active=False, deactivated_at=timezone.now())
                
                results['total_invalidated'] = sum(results.values()) - results['total_invalidated']
                
                # Log critical security event
                log_security_event(
                    event_type='emergency_all_tokens_invalidated',
                    description=f'EMERGENCY: All system tokens invalidated: {reason}',
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                    metadata={
                        'reason': reason,
                        'admin_user': admin_user.email,
                        'invalidation_results': results,
                        'severity': 'CRITICAL'
                    }
                )
                
                logger.critical(f"EMERGENCY: All tokens invalidated by {admin_user.email}: {results}")
                return results
                
        except Exception as e:
            logger.error(f"Error in emergency token invalidation: {str(e)}")
            return {'error': str(e)}
    
    @classmethod
    def _blacklist_token(cls, token_value: str, reason: str):
        """Add token to blacklist cache"""
        try:
            cache_key = f"{cls.BLACKLIST_CACHE_PREFIX}{hash(token_value)}"
            cache.set(cache_key, {
                'reason': reason,
                'blacklisted_at': timezone.now().isoformat()
            }, cls.BLACKLIST_CACHE_TIMEOUT)
        except Exception as e:
            logger.error(f"Error blacklisting token: {str(e)}")


# Global service instance
token_invalidation_service = TokenInvalidationService()
