"""
Comprehensive Email Service for OAuth2 Authentication System
Handles all email notifications with templates and error handling
"""

from asyncio import constants
import logging
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
# Removed static file imports - using hosted URLs instead
from typing import Dict, Any, Optional, List
import traceback

logger = logging.getLogger(__name__)


class EmailService:
    """
    Centralized email service for all authentication-related notifications
    """
    
    def __init__(self):
        self.from_email = getattr(settings, 'EMAIL_HOST_USER', '<EMAIL>')
        self.support_email = getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>')
        self.base_frontend_url = getattr(settings, 'FRONTEND_URL', 'https://agritram.com')

        # Role-specific frontend URLs
        self.role_urls = {
            'farmer': getattr(settings, 'FARMER_FRONTEND_URL', f"{self.base_frontend_url}/farmer"),
            'trader': getattr(settings, 'TRADER_FRONTEND_URL', f"{self.base_frontend_url}/trader"),
            'manufacturer': getattr(settings, 'MANUFACTURER_FRONTEND_URL', f"{self.base_frontend_url}/manufacturer"),
        }

        # Company logo URLs using FRONTEND_URL from settings
        self.logo_base_url = getattr(settings, 'FRONTEND_URL')
        self.serves_provider_logo = f'{self.logo_base_url}/serves_provider_logo.svg'
        self.agritram_logo = f'{self.logo_base_url}/agritram_logo.svg'

    def _get_logo_urls(self):
        """
        Get logo URLs from hosted server
        """
        return {
            'serves_provider_logo_url': self.serves_provider_logo,
            'agritram_logo_url': self.agritram_logo
        }

    def _get_role_specific_url(self, user, path: str = '') -> str:
        """
        Get role-specific frontend URL for the user
        """
        user_role = getattr(user, 'role', 'farmer').lower()
        base_url = self.role_urls.get(user_role, self.role_urls['farmer'])

        if path:
            return f"{base_url}/{path.lstrip('/')}"
        return base_url

    def _send_email(self,
                   subject: str,
                   template_name: str,
                   context: Dict[str, Any],
                   recipient_email: str,
                   fail_silently: bool = True) -> bool:
        """
        Internal method to send emails with template rendering
        """
        try:
            # Get role-specific frontend URL
            user = context.get('user')
            role_specific_url = self._get_role_specific_url(user) if user else self.base_frontend_url

            # Get logo URLs
            logo_urls = self._get_logo_urls()

            # Add common context variables
            context.update({
                'support_email': self.support_email,
                'frontend_url': role_specific_url,
                'current_year': timezone.now().year,
                'company_name': 'Agritram',
                'serves_provider_logo_url': logo_urls['serves_provider_logo_url'],
                'agritram_logo_url': logo_urls['agritram_logo_url']
            })
            
            # Render HTML template
            try:
                html_content = render_to_string(f'emails/{template_name}', context)
            except Exception as e:
                logger.warning(f"Failed to render HTML template {template_name}: {str(e)}")
                html_content = None
            
            # Create plain text version
            plain_content = self._create_plain_text_version(context, template_name)
            
            # Send email
            if html_content:
                msg = EmailMultiAlternatives(
                    subject=subject,
                    body=plain_content,
                    from_email=self.from_email,
                    to=[recipient_email]
                )
                msg.attach_alternative(html_content, "text/html")
                msg.send()
            else:
                send_mail(
                    subject=subject,
                    message=plain_content,
                    from_email=self.from_email,
                    recipient_list=[recipient_email],
                    fail_silently=fail_silently
                )
            
            logger.info(f"Email sent successfully to {recipient_email}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def _create_plain_text_version(self, context: Dict[str, Any], template_name: str) -> str:
        """
        Create a plain text version of the email
        """
        user = context.get('user')
        user_name = user.name if user else 'User'
        role_specific_url = self._get_role_specific_url(user) if user else self.base_frontend_url
        
        if 'otp' in template_name:
            return f"""
Hi {user_name},

We detected a login attempt from a new device. Please use this verification code:

{context.get('otp_code', 'N/A')}

This code expires in 5 minutes.

Device: {context.get('device_name', 'Unknown')}
IP: {context.get('ip_address', 'Unknown')}

If this wasn't you, please contact support immediately.

Best regards,
Agritram Security Team
            """
        elif 'login' in template_name:
            return f"""
Hi {user_name},

We detected a new login to your Agritram account:

Device: {context.get('device_name', 'Unknown')}
IP: {context.get('ip_address', 'Unknown')}
Time: {context.get('login_time', timezone.now()).strftime('%Y-%m-%d %H:%M:%S UTC')}

If this was you, no action needed. If not, contact support immediately.

Best regards,
Agritram Security Team
            """
        elif 'registration' in template_name:
            registration_time = context.get('registration_time', timezone.now()).strftime('%Y-%m-%d %H:%M:%S UTC')
            return f"""
Hi {user_name},

Welcome to Agritram! Please activate your account:

{context.get('activation_url', 'N/A')}

This link expires in 24 hours.

Registration time: {registration_time}

Best regards,
Agritram Team
            """
        elif 'welcome' in template_name:
            return f"""
Hi {user_name},

Welcome to Agritram! Your account is now active.

Login at: {role_specific_url}/login

Best regards,
Agritram Team
            """
        elif 'security' in template_name:
            return f"""
Hi {user_name},

SECURITY ALERT: {context.get('alert_type', 'Unknown Alert')}

Details: {context.get('details', 'Please check your account')}

Please contact support immediately: {self.support_email}

Best regards,
Agritram Security Team
            """
        else:
            return f"""
Hi {user_name},

You have received a notification from Agritram.

Please check your account or contact support if you have questions.

Best regards,
Agritram Team
            """
    
    def send_otp_verification(self, user, otp_code: str, device_name: str, ip_address: str) -> bool:
        """
        Send OTP verification email for new device
        """
        context = {
            'user': user,
            'otp_code': otp_code,
            'device_name': device_name,
            'ip_address': ip_address,
            'verification_time': timezone.now()
        }
        
        return self._send_email(
            subject="Device Verification Required - Agritram",
            template_name="otp_verification.html",
            context=context,
            recipient_email=user.email
        )
    
    def send_login_notification(self, user, device_name: str, ip_address: str, user_agent: str) -> bool:
        """
        Send login notification email
        """
        context = {
            'user': user,
            'device_name': device_name,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'login_time': timezone.now()
        }
        
        return self._send_email(
            subject="New Login to Your Agritram Account",
            template_name="login_notification.html",
            context=context,
            recipient_email=user.email
        )
    
    def send_registration_activation(self, user, activation_url: str, user_agent: str) -> bool:
        """
        Send registration activation email
        """
        context = {
            'user': user,
            'activation_url': activation_url,
            'user_agent': user_agent,
            'registration_time': timezone.now(),
        }
        
        return self._send_email(
            subject="Welcome to Agritram - Activate Your Account",
            template_name="registration_activation.html",
            context=context,
            recipient_email=user.email
        )
    
    def send_welcome_email(self, user, ip_address: str, user_agent: str) -> bool:
        """
        Send welcome email after account activation
        """
        role_specific_url = self._get_role_specific_url(user)
        context = {
            'user': user,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'activation_time': timezone.now(),
            'login_url': f"{role_specific_url}/login"
        }
        
        return self._send_email(
            subject="Welcome to Agritram - Your Account is Active!",
            template_name="welcome_email.html",
            context=context,
            recipient_email=user.email
        )
    
    def send_security_alert(self, user, alert_type: str, details: str, metadata: Optional[Dict] = None) -> bool:
        """
        Send security alert email
        """
        context = {
            'user': user,
            'alert_type': alert_type,
            'details': details,
            'alert_time': timezone.now(),
            'metadata': metadata or {}
        }
        
        return self._send_email(
            subject=f"Security Alert - {alert_type}",
            template_name="security_alert.html",
            context=context,
            recipient_email=user.email
        )
    
    def send_password_reset(self, user, reset_url: str, ip_address: str = None, user_agent: str = None) -> bool:
        """
        Send enhanced password reset email with security context
        """
        context = {
            'user': user,
            'reset_url': reset_url,
            'reset_time': timezone.now(),
            'ip_address': ip_address,
            'user_agent': user_agent
        }

        return self._send_email(
            subject="Password Reset Request - Agritram",
            template_name="reset_password_email.html",
            context=context,
            recipient_email=user.email
        )
    
    def send_bulk_notification(self, users: List, subject: str, template_name: str, context: Dict[str, Any]) -> Dict[str, int]:
        """
        Send bulk notifications to multiple users
        """
        results = {'success': 0, 'failed': 0}
        
        for user in users:
            user_context = context.copy()
            user_context['user'] = user
            
            success = self._send_email(
                subject=subject,
                template_name=template_name,
                context=user_context,
                recipient_email=user.email
            )
            
            if success:
                results['success'] += 1
            else:
                results['failed'] += 1
        
        logger.info(f"Bulk email results: {results['success']} sent, {results['failed']} failed")
        return results


# Global email service instance
email_service = EmailService()
