# OAuth2 Testing - Detailed Execution Guide

## Pre-Test Setup

### Environment Configuration
```bash
# Backend Setup
cd projects/backend
python manage.py migrate
python manage.py collectstatic
python manage.py runserver 8000

# Frontend Setup - Run all three projects
cd projects/farmer && npm run dev    # Port 5173
cd projects/trader && npm run dev    # Port 5174  
cd projects/manufacturer && npm run dev # Port 5175
```

### Test Data Preparation
```sql
-- <PERSON>reate test users for each role
INSERT INTO user_user (name, email, password, role, is_active) VALUES
('Test Farmer', '<EMAIL>', 'hashed_password', 'farmer', true),
('Test Trader', '<EMAIL>', 'hashed_password', 'trader', true),
('Test Manufacturer', '<EMAIL>', 'hashed_password', 'manufacturer', true);

-- Create locked account for testing
INSERT INTO user_user (name, email, password, role, is_active, failed_login_attempts, lockout_until) VALUES
('Locked User', '<EMAIL>', 'hashed_password', 'farmer', true, 5, NOW() + INTERVAL '24 hours');
```

## Critical Test Scenarios

### 1. Account Lockout Testing (TC010-TC013)

#### Progressive Lockout Test:
```python
# Test Script Example
import requests
import time

def test_progressive_lockout():
    url = "http://localhost:8000/api/users/login/"
    email = "<EMAIL>"
    
    # Make 5 failed attempts
    for i in range(5):
        response = requests.post(url, {
            "email": email,
            "password": "wrong_password",
            "device_id": "test_device"
        })
        print(f"Attempt {i+1}: {response.status_code}")
    
    # 6th attempt should trigger lockout
    response = requests.post(url, {
        "email": email,
        "password": "wrong_password",
        "device_id": "test_device"
    })
    
    assert response.status_code == 400
    assert "locked" in response.json()["message"].lower()
```

#### 5-Hour Window Reset Test:
```python
def test_failed_attempt_window():
    # Make 3 failed attempts
    make_failed_attempts(3)
    
    # Wait 6 hours (simulate with database update)
    simulate_time_passage(hours=6)
    
    # Make 2 more attempts - should not trigger lockout
    make_failed_attempts(2)
    
    # Verify account not locked
    assert not is_account_locked()
```

### 2. Cross-Device Security Testing (TC006, TC047)

#### Registration-Activation Device Mismatch:
```javascript
// Frontend Test
describe('Cross-Device Security', () => {
  test('should warn on device mismatch', async () => {
    // Register from Device A
    const registrationResponse = await fetch('/api/users/register/', {
      method: 'POST',
      headers: { 'User-Agent': 'Device-A-Browser' },
      body: JSON.stringify({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'SecurePass123!',
        role: 'farmer',
        device_id: 'device-a-123'
      })
    });
    
    // Attempt activation from Device B
    const activationResponse = await fetch('/api/users/activate/uid/token/', {
      headers: { 'User-Agent': 'Device-B-Browser' },
      body: JSON.stringify({ device_id: 'device-b-456' })
    });
    
    // Should trigger security validation
    expect(activationResponse.status).toBe(200);
    // Check for security event in logs
  });
});
```

### 3. Token Rotation Testing (TC017, TC037)

#### Automatic Token Refresh:
```javascript
// Frontend Token Refresh Test
describe('Token Management', () => {
  test('should automatically refresh expired tokens', async () => {
    // Set up user with near-expired token
    const expiredToken = generateExpiredToken();
    localStorage.setItem('auth-token', expiredToken);
    
    // Make authenticated request
    const response = await authenticatedFetch('/api/protected-endpoint/');
    
    // Should automatically refresh token
    expect(response.status).toBe(200);
    
    // Verify new token stored
    const newToken = localStorage.getItem('auth-token');
    expect(newToken).not.toBe(expiredToken);
  });
});
```

### 4. Role-Based Access Testing (TC032-TC033, TC048)

#### Role-Based Route Protection:
```javascript
// Test for each frontend project
const roleTests = [
  { role: 'farmer', port: 5173, allowedRoutes: ['/dashboard', '/buy', '/sell'] },
  { role: 'trader', port: 5174, allowedRoutes: ['/dashboard', '/inventory', '/shipments'] },
  { role: 'manufacturer', port: 5175, allowedRoutes: ['/dashboard', '/orders', '/production'] }
];

roleTests.forEach(({ role, port, allowedRoutes }) => {
  describe(`${role} Role Access`, () => {
    test('should access allowed routes', async () => {
      await loginAs(role);
      
      for (const route of allowedRoutes) {
        const response = await fetch(`http://localhost:${port}${route}`);
        expect(response.status).toBe(200);
      }
    });
    
    test('should block unauthorized routes', async () => {
      await loginAs(role);
      
      // Try accessing other role's routes
      const otherPorts = [5173, 5174, 5175].filter(p => p !== port);
      for (const otherPort of otherPorts) {
        const response = await fetch(`http://localhost:${otherPort}/dashboard`);
        expect(response.status).toBe(403);
      }
    });
  });
});
```

### 5. Onboarding Flow Testing (TC034-TC035)

#### Complete Onboarding Flow:
```javascript
describe('Onboarding Flow', () => {
  test('should complete 6-step onboarding', async () => {
    // Login as new user
    await loginAsNewUser();
    
    // Should redirect to onboarding
    expect(window.location.pathname).toBe('/onboarding');
    
    // Step 1: Welcome
    await clickNext();
    
    // Step 2: Wallet Connection
    await connectWallet();
    
    // Step 3: Account Selection
    await selectAccount();
    
    // Step 4: Profile Setup
    await fillProfile();
    
    // Step 5: Preferences
    await setPreferences();
    
    // Step 6: Completion
    await completeOnboarding();
    
    // Should redirect to dashboard
    expect(window.location.pathname).toBe('/dashboard');
    
    // User should be marked as onboarded
    const user = getCurrentUser();
    expect(user.onboarding_completed).toBe(true);
  });
});
```

## API Testing Examples

### Registration API Test:
```python
def test_registration_api():
    url = "http://localhost:8000/api/users/register/"
    
    # Valid registration
    response = requests.post(url, {
        "name": "Test User",
        "email": "<EMAIL>", 
        "password": "SecurePass123!",
        "role": "farmer",
        "device_id": "test_device_123"
    })
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response structure
    assert "user" in data
    assert "activation_required" in data
    assert "oauth2_client_id" in data
    assert data["activation_required"] == True
    
    # Verify user created in database
    user = User.objects.get(email="<EMAIL>")
    assert user.is_active == False
    assert user.role == "farmer"
    
    # Verify OAuth2 application created
    oauth_app = Application.objects.get(user=user)
    assert oauth_app.client_id.startswith("agritram-")
```

### Login API Test:
```python
def test_login_api():
    url = "http://localhost:8000/api/users/login/"
    
    response = requests.post(url, {
        "email": "<EMAIL>",
        "password": "correct_password",
        "device_id": "test_device_123"
    })
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify token structure
    assert "access_token" in data
    assert "refresh_token" in data
    assert "expires_in" in data
    assert data["expires_in"] == 900  # 15 minutes
    
    # Verify JWT token
    import jwt
    decoded = jwt.decode(data["access_token"], verify=False)
    assert decoded["user_id"]
    assert decoded["device_id"] == "test_device_123"
```

## Frontend Testing Examples

### Login Form Test:
```javascript
import { render, fireEvent, waitFor } from '@testing-library/react';
import Login from '../pages/Login';

test('should handle login form submission', async () => {
  const { getByLabelText, getByRole } = render(<Login />);
  
  // Fill form
  fireEvent.change(getByLabelText(/email/i), {
    target: { value: '<EMAIL>' }
  });
  fireEvent.change(getByLabelText(/password/i), {
    target: { value: 'password123' }
  });
  
  // Submit form
  fireEvent.click(getByRole('button', { name: /login/i }));
  
  // Wait for response
  await waitFor(() => {
    expect(window.location.pathname).toBe('/dashboard');
  });
});
```

### Protected Route Test:
```javascript
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ProtectedRoute from '../components/auth/ProtectedRoute';

test('should redirect unauthenticated users', () => {
  // Mock unauthenticated state
  jest.mock('../stores/authStore', () => ({
    useIsAuthenticated: () => false
  }));
  
  const { container } = render(
    <MemoryRouter initialEntries={['/dashboard']}>
      <ProtectedRoute requireAuth={true}>
        <div>Protected Content</div>
      </ProtectedRoute>
    </MemoryRouter>
  );
  
  // Should redirect to login
  expect(window.location.pathname).toBe('/login');
});
```

## Performance Testing

### Load Testing Script:
```python
import asyncio
import aiohttp
import time

async def load_test_login():
    async with aiohttp.ClientSession() as session:
        tasks = []
        
        # Create 100 concurrent login requests
        for i in range(100):
            task = session.post('http://localhost:8000/api/users/login/', json={
                'email': f'user{i}@test.com',
                'password': 'password123',
                'device_id': f'device_{i}'
            })
            tasks.append(task)
        
        start_time = time.time()
        responses = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Analyze results
        success_count = sum(1 for r in responses if r.status == 200)
        avg_time = (end_time - start_time) / len(responses)
        
        print(f"Success rate: {success_count}/{len(responses)}")
        print(f"Average response time: {avg_time:.2f}s")

# Run load test
asyncio.run(load_test_login())
```

## Security Testing

### SQL Injection Test:
```python
def test_sql_injection():
    malicious_payloads = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --"
    ]
    
    for payload in malicious_payloads:
        response = requests.post('/api/users/login/', {
            'email': payload,
            'password': 'test'
        })
        
        # Should not cause SQL error or unauthorized access
        assert response.status_code in [400, 401]
        assert 'error' not in response.text.lower()
```

### XSS Prevention Test:
```javascript
test('should prevent XSS in form inputs', async () => {
  const xssPayload = '<script>alert("XSS")</script>';
  
  const { getByLabelText } = render(<RegisterForm />);
  
  fireEvent.change(getByLabelText(/name/i), {
    target: { value: xssPayload }
  });
  
  // Submit form
  fireEvent.submit(getByRole('form'));
  
  // Verify XSS payload is sanitized
  const nameField = getByLabelText(/name/i);
  expect(nameField.value).not.toContain('<script>');
});
```

## Test Automation

### CI/CD Pipeline Integration:
```yaml
# .github/workflows/test.yml
name: OAuth2 Tests
on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run backend tests
        run: python manage.py test
  
  frontend-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: [farmer, trader, manufacturer]
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: 18
      - name: Install dependencies
        run: cd projects/${{ matrix.project }} && npm install
      - name: Run tests
        run: cd projects/${{ matrix.project }} && npm test
```

This execution guide provides practical examples and scripts for implementing the comprehensive test cases outlined in the JIRA export file.
