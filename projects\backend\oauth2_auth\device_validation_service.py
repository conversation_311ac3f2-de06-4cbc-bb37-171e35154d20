"""
Cross-Device Authentication Validation Service

Provides fintech-grade device consistency validation across registration,
activation, and login flows to prevent cross-device security issues.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from .models import <PERSON><PERSON><PERSON><PERSON>, SecurityEvent
from .utils import log_security_event, get_client_ip, generate_device_fingerprint

User = get_user_model()
logger = logging.getLogger(__name__)


class DeviceValidationService:
    """
    Service for validating device consistency across authentication flows
    """
    
    # Cache keys for tracking device flows
    REGISTRATION_DEVICE_KEY = "reg_device_{user_id}"
    ACTIVATION_DEVICE_KEY = "act_device_{user_id}"
    LOGIN_DEVICE_KEY = "login_device_{user_id}"
    
    # Security thresholds
    MAX_DEVICE_CHANGES = 3  # Maximum device changes allowed in flow
    DEVICE_FLOW_TIMEOUT = 3600  # 1 hour timeout for device flow tracking
    
    @classmethod
    def track_registration_device(cls, user, device_id, request) -> Dict[str, Any]:
        """
        Track device information during registration
        
        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object
            
        Returns:
            dict: Device tracking information
        """
        try:
            device_info = cls._extract_device_info(device_id, request)
            
            # Store device info in cache for cross-flow validation
            cache_key = cls.REGISTRATION_DEVICE_KEY.format(user_id=user.id)
            cache.set(cache_key, device_info, cls.DEVICE_FLOW_TIMEOUT)
            
            # Log device tracking
            log_security_event(
                user=user,
                event_type='registration_device_tracked',
                description='Device information tracked during registration',
                ip_address=device_info['ip_address'],
                user_agent=device_info['user_agent'],
                device_id=device_id,
                metadata={
                    'device_fingerprint': device_info['fingerprint'],
                    'tracking_started': timezone.now().isoformat()
                }
            )
            
            logger.info(f"Tracking registration device for user {user.email}")
            return device_info
            
        except Exception as e:
            logger.error(f"Error tracking registration device: {str(e)}")
            return {}
    
    @classmethod
    def validate_activation_device(cls, user, device_id, request) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Validate device consistency during account activation
        
        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object
            
        Returns:
            Tuple of (is_valid, message, validation_details)
        """
        try:
            current_device_info = cls._extract_device_info(device_id, request)
            
            # Get registration device info
            reg_cache_key = cls.REGISTRATION_DEVICE_KEY.format(user_id=user.id)
            registration_device = cache.get(reg_cache_key)
            
            validation_result = cls._validate_device_consistency(
                registration_device, 
                current_device_info, 
                'activation'
            )
            
            # Store activation device info
            act_cache_key = cls.ACTIVATION_DEVICE_KEY.format(user_id=user.id)
            cache.set(act_cache_key, current_device_info, cls.DEVICE_FLOW_TIMEOUT)
            
            # Log validation result
            log_security_event(
                user=user,
                event_type='activation_device_validated',
                description=f'Device validation during activation: {"passed" if validation_result["is_valid"] else "failed"}',
                ip_address=current_device_info['ip_address'],
                user_agent=current_device_info['user_agent'],
                device_id=device_id,
                metadata={
                    'validation_result': validation_result,
                    'security_score': validation_result.get('security_score', 0)
                }
            )
            
            return validation_result['is_valid'], validation_result['message'], validation_result
            
        except Exception as e:
            logger.error(f"Error validating activation device: {str(e)}")
            return False, "Device validation failed", {'error': str(e)}
    
    @classmethod
    def validate_login_device(cls, user, device_id, request) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Validate device consistency during login
        
        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object
            
        Returns:
            Tuple of (is_valid, message, validation_details)
        """
        try:
            current_device_info = cls._extract_device_info(device_id, request)
            
            # Get previous device info (prefer activation, fallback to registration)
            act_cache_key = cls.ACTIVATION_DEVICE_KEY.format(user_id=user.id)
            reg_cache_key = cls.REGISTRATION_DEVICE_KEY.format(user_id=user.id)
            
            previous_device = cache.get(act_cache_key) or cache.get(reg_cache_key)
            
            validation_result = cls._validate_device_consistency(
                previous_device, 
                current_device_info, 
                'login'
            )
            
            # Store login device info
            login_cache_key = cls.LOGIN_DEVICE_KEY.format(user_id=user.id)
            cache.set(login_cache_key, current_device_info, cls.DEVICE_FLOW_TIMEOUT)
            
            # Log validation result
            log_security_event(
                user=user,
                event_type='login_device_validated',
                description=f'Device validation during login: {"passed" if validation_result["is_valid"] else "failed"}',
                ip_address=current_device_info['ip_address'],
                user_agent=current_device_info['user_agent'],
                device_id=device_id,
                metadata={
                    'validation_result': validation_result,
                    'security_score': validation_result.get('security_score', 0)
                }
            )
            
            return validation_result['is_valid'], validation_result['message'], validation_result
            
        except Exception as e:
            logger.error(f"Error validating login device: {str(e)}")
            return False, "Device validation failed", {'error': str(e)}
    
    @classmethod
    def _extract_device_info(cls, device_id, request) -> Dict[str, Any]:
        """Extract device information from request"""
        return {
            'device_id': device_id,
            'ip_address': get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'fingerprint': generate_device_fingerprint(request),
            'timestamp': timezone.now().isoformat()
        }
    
    @classmethod
    def _validate_device_consistency(cls, previous_device, current_device, flow_type) -> Dict[str, Any]:
        """
        Validate device consistency between flows
        
        Args:
            previous_device: Previous device information
            current_device: Current device information
            flow_type: Type of flow ('activation', 'login')
            
        Returns:
            dict: Validation result with security scoring
        """
        if not previous_device:
            # No previous device to compare - allow but with lower security score
            return {
                'is_valid': True,
                'message': 'No previous device information available',
                'security_score': 50,  # Medium security score
                'warnings': ['no_previous_device_data']
            }
        
        security_score = 100
        warnings = []
        
        # Check device ID consistency
        if previous_device.get('device_id') != current_device.get('device_id'):
            security_score -= 30
            warnings.append('device_id_mismatch')
        
        # Check IP address consistency
        if previous_device.get('ip_address') != current_device.get('ip_address'):
            security_score -= 20
            warnings.append('ip_address_change')
        
        # Check device fingerprint consistency
        if previous_device.get('fingerprint') != current_device.get('fingerprint'):
            security_score -= 25
            warnings.append('device_fingerprint_change')
        
        # Check user agent consistency (less critical)
        if previous_device.get('user_agent') != current_device.get('user_agent'):
            security_score -= 10
            warnings.append('user_agent_change')
        
        # Determine if validation passes based on security score
        is_valid = security_score >= 60  # Require at least 60% security score
        
        if not is_valid:
            message = f"Device validation failed for {flow_type}: security score {security_score}%"
        else:
            message = f"Device validation passed for {flow_type}: security score {security_score}%"
        
        return {
            'is_valid': is_valid,
            'message': message,
            'security_score': security_score,
            'warnings': warnings,
            'flow_type': flow_type,
            'comparison': {
                'previous': previous_device,
                'current': current_device
            }
        }
    
    @classmethod
    def cleanup_device_tracking(cls, user_id):
        """Clean up device tracking cache for a user"""
        try:
            cache_keys = [
                cls.REGISTRATION_DEVICE_KEY.format(user_id=user_id),
                cls.ACTIVATION_DEVICE_KEY.format(user_id=user_id),
                cls.LOGIN_DEVICE_KEY.format(user_id=user_id)
            ]
            
            for key in cache_keys:
                cache.delete(key)
            
            logger.info(f"Cleaned up device tracking for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up device tracking: {str(e)}")


# Global service instance
device_validation_service = DeviceValidationService()
