from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from oauth2_auth.authentication import OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication
from rest_framework.response import Response
from rest_framework import status
from decimal import Decimal
from django.db import transaction
from typing import Dict, Any
from rest_framework import permissions
from user.permissions import UserStatusMixin

from agritram.message_utils import handle_serializer_errors, handle_exception_with_logging, StandardSuccessResponse
from agritram.exceptions import (
    raise_validation_error,
    raise_authorization_error,
    raise_not_found_error,
    raise_business_logic_error
)

from crops.models import CropTransfer
from inventory.models import InventoryCropStatus, InventoryQuantity
from .serializers import ProofOfUnlockSerializer, MilestoneSerializer
from django.utils import timezone
from .models import Milestone, ProofOfUnlock
from django.shortcuts import get_object_or_404
from crops.serializers import CropTransferSerializer
from user.models import User
import logging

logger = logging.getLogger(__name__)


class ProofOfUnlockCreatePermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for creating proof of unlock transactions.
    Business Rule: Only manufacturers (buyers) and traders (sellers) can create.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"ProofOfUnlock create permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can create proof of unlock
        return user_role in ['manufacturer', 'trader', 'admin']


class ProofOfUnlockViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for viewing proof of unlock transactions.
    Business Rule: Only the buyer and seller involved can view.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"ProofOfUnlock view permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can view proof of unlock
        return user_role in ['manufacturer', 'trader', 'admin']

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can view everything
        if user_role == 'admin':
            return True

        # Only buyer and seller can view the transaction
        if hasattr(obj, 'buyer') and hasattr(obj, 'seller'):
            return request.user in [obj.buyer, obj.seller]
        elif hasattr(obj, 'buyer_id') and hasattr(obj, 'seller_id'):
            return request.user.id in [obj.buyer_id, obj.seller_id]

        return False


class MilestoneUpdatePermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for updating milestones.
    Business Rule: Both buyer (manufacturer) and seller (trader) can update milestones.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"Milestone update permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can update milestones
        return user_role in ['manufacturer', 'trader', 'admin']


# Create your views here.
@api_view(["POST"])
@permission_classes([ProofOfUnlockCreatePermission])  # Manufacturers and traders can create
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def create_proof_of_unlock(request) -> Response:
    """
    Create a new proof of unlock transaction.
    Business Rule: Only manufacturers (buyers) and traders (sellers) can create.
    Business Flow: Manufacturer creates proof of unlock when buying from trader.
    """
    try:
        # Validate user role for creation
        if request.user.role not in ['manufacturer', 'trader', 'admin']:
            raise_authorization_error(
                message="Only manufacturers and traders can create proof of unlock transactions",
                details="This operation is restricted to manufacturer and trader roles"
            )

        # Prepare request data with default values
        request_data = _prepare_request_data(request.data)
        print('Request data', request_data)

        # Validate and create crop transfer
        crop_transfer = _create_crop_transfer(request_data)
        if not crop_transfer:
            raise_validation_error(
                message="Invalid crop transfer data",
                details="Failed to create crop transfer with provided data"
            )

        # Process the transaction within a database transaction
        with transaction.atomic():
            # Create proof of unlock
            proof_of_unlock = _create_proof_of_unlock(request_data)

            # Update inventory
            _update_inventory(request_data)

            return StandardSuccessResponse.record_created(
                message="Proof of unlock created successfully",
                details=f"New proof of unlock transaction created with ID: {proof_of_unlock.tx_id}",
                record_data=ProofOfUnlockSerializer(proof_of_unlock).data
            )

    except User.DoesNotExist:
        raise_not_found_error(
            message="Seller with the provided account address not found",
            details="No user found with the provided seller account address",
            resource_type="user"
        )
    except Exception as e:
        print('Error', e)
        return handle_exception_with_logging(e, "proof of unlock creation")

def _prepare_request_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare and validate request data with default values."""
    data["created_at"] = timezone.now()
    data["last_updated"] = timezone.now()
    data.setdefault("release_amount", 0)
    data.setdefault("status", "active")
    return data

def _create_crop_transfer(data: Dict[str, Any]) -> bool:
    """Create and validate crop transfer."""
    crop_transfer = CropTransfer.objects.create(
        transaction_id=data["tx_id"],
        crop_id=data["crops"][0]["crop_id"],
        from_user=User.objects.get(account_address=data["seller"]),
        to_user=User.objects.get(account_address=data["buyer"]),
        cost=data["total_amount"],
        status="PENDING"
    )
    
    if crop_transfer:
        return True
    return False

def _create_proof_of_unlock(data: Dict[str, Any]) -> ProofOfUnlock:
    """Create proof of unlock transaction."""
    serializer = ProofOfUnlockSerializer(data=data)
    if not serializer.is_valid():
        raise ValueError(serializer.errors)
    return serializer.save()

def _update_inventory(data: Dict[str, Any]) -> None:
    """Update inventory quantities and status."""
    seller = User.objects.get(
        account_address=data["seller"]
    )
    
    quantity = Decimal(str(data["crops"][0]["quantity"]))
    crop_id = data["crops"][0]["crop_id"]
    
    # Update inventory quantity
    inventory_quantity = InventoryQuantity.objects.get(
        trader=seller.id,
    )
    inventory_quantity.ready_to_sell_quantity -= quantity
    inventory_quantity.ready_to_sell_batches -= 1
    inventory_quantity.sold_quantity += quantity
    inventory_quantity.sold_batches += 1
    inventory_quantity.save()
    
    # Update inventory crop status
    inventory_crop_status = InventoryCropStatus.objects.select_related('crop').get(
        trader=seller.id,
        crop=crop_id
    )
    inventory_crop_status.status = "in_progress"
    inventory_crop_status.save()


@api_view(["GET"])
@permission_classes([ProofOfUnlockViewPermission])  # Only buyer and seller involved can view
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def get_proof_of_unlock_by_user(request):
    """
    Retrieve proof of unlock transactions for the authenticated user.
    Business Rule: Only the buyer and seller involved can view their transactions.
    """
    try:
        # Filter transactions where the user is either the buyer or the seller
        if request.user.role == "manufacturer":
            # Manufacturers see transactions where they are the buyer
            transactions = ProofOfUnlock.objects.filter(
                buyer_id=request.user.id
            ).order_by('-created_at')
        elif request.user.role == "trader":
            # Traders see transactions where they are the seller
            transactions = ProofOfUnlock.objects.filter(
                seller_id=request.user.id
            ).order_by('-created_at')
        elif request.user.role == "admin":
            # Admins can see all transactions
            transactions = ProofOfUnlock.objects.all().order_by('-created_at')
        else:
            raise_authorization_error(
                message="Only manufacturers, traders, and admins can access proof of unlock transactions",
                details="This operation is restricted to manufacturer, trader, and admin roles"
            )

        serializer = ProofOfUnlockSerializer(transactions, many=True)
        return StandardSuccessResponse.data_retrieved(
            message="Proof of unlock transactions retrieved successfully",
            details=f"Retrieved {len(serializer.data)} proof of unlock transactions for user",
            data=serializer.data
        )
    except ProofOfUnlock.DoesNotExist:
        raise_not_found_error(
            message="No transactions found for this user",
            details="No proof of unlock transactions found for the authenticated user",
            resource_type="proof_of_unlock"
        )


@api_view(["PUT"])
@permission_classes([MilestoneUpdatePermission])  # Both buyer and seller can update milestones
@authentication_classes([OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication])
def update_milestone(request):
    """
    Update milestone status and transaction status.
    Business Rule: Both buyer (manufacturer) and seller (trader) can update milestones.
    Business Flow: Trader updates milestones as they fulfill delivery, Manufacturer releases payments.

    Expected request data:
    {
        "milestone": {
            "id": "milestone_id",
            "status": "new_status"
        },
        "tx_id": "transaction_id",
        "status": "new_transaction_status"
    }
    """
    try:
        # Get milestone and transaction
        milestone = get_object_or_404(Milestone, id=request.data["milestone"]["id"])
        transaction_obj: ProofOfUnlock = get_object_or_404(ProofOfUnlock, tx_id=request.data["tx_id"])

        # Verify milestone belongs to the transaction
        if milestone.transaction != transaction_obj:
            raise_validation_error(
                message="Milestone does not belong to the specified transaction",
                details="The milestone ID provided is not associated with the specified transaction"
            )

        # Verify user is involved in the transaction
        if request.user.role not in ['admin'] and request.user.id not in [transaction_obj.buyer_id, transaction_obj.seller_id]:
            raise_authorization_error(
                message="You can only update milestones for transactions you're involved in",
                details="Only the buyer, seller, or admin can update milestones for this transaction"
            )

        # Update milestone status
        milestone.status = request.data["milestone"]["status"]
        if request.data["milestone"]["status"] == "completed":
            milestone.completed_date = timezone.now()
        milestone.save()

        # Handle payment release (typically done by manufacturer/buyer)
        if request.data["milestone"]["status"] == "released":
            if request.user.role == 'manufacturer' or request.user.role == 'admin':
                transaction_obj.release_amount = transaction_obj.total_amount + milestone.amount
                transaction_obj.save()
            else:
                raise_authorization_error(
                    message="Only manufacturers can release payments",
                    details="Payment release is restricted to manufacturer role users"
                )

        # Update transaction status if provided
        if "status" in request.data and request.data["status"] == "completed":
            transaction_obj.status = request.data["status"]
            transaction_obj.last_updated = timezone.now()
            transaction_obj.save()

            # Update inventory crop status
            try:
                inventory_crop_status = InventoryCropStatus.objects.select_for_update().get(
                    trader=transaction_obj.seller.id,
                    crop=transaction_obj.crops[0].crop_id
                )
                inventory_crop_status.status = "sold"
                inventory_crop_status.save()
            except (InventoryCropStatus.DoesNotExist, IndexError, AttributeError):
                pass  # Handle gracefully if inventory status doesn't exist

            # Update crop transfer status
            try:
                crop_transfer = CropTransfer.objects.select_for_update().get(
                    transaction_id=transaction_obj.tx_id,
                    crop_id=transaction_obj.crops[0].crop_id
                )
                crop_transfer.status = "COMPLETED"
                crop_transfer.save()
            except (CropTransfer.DoesNotExist, IndexError, AttributeError):
                pass  # Handle gracefully if crop transfer doesn't exist

        # Return updated milestone data
        serializer = MilestoneSerializer(milestone)
        return StandardSuccessResponse.record_updated(
            message="Milestone updated successfully",
            details=f"Milestone {milestone.id} status updated to '{milestone.status}'",
            record_data=serializer.data
        )

    except KeyError as e:
        print('KeyError', e)
        raise_validation_error(
            message=f"Missing required field: {str(e)}",
            details="Required field is missing from the request data"
        )
    except Exception as e:
        return handle_exception_with_logging(e, "milestone update")
