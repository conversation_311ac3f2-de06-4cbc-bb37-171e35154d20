from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from oauth2_provider.models import Grant, AccessToken, RefreshToken
from oauth2_auth.models import SecurityEvent
from oauth2_auth.token_invalidation_service import token_invalidation_service
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up expired OAuth2 tokens and grants'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to keep expired tokens for audit purposes'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
        parser.add_argument(
            '--cleanup-events',
            action='store_true',
            help='Also cleanup old security events (older than 90 days)'
        )
    
    def handle(self, *args, **options):
        days_to_keep = options['days']
        dry_run = options['dry_run']
        cleanup_events = options['cleanup_events']
        
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        
        self.stdout.write(f"Cleaning up tokens expired before: {cutoff_date}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No actual deletions will occur"))
        
        # Clean up expired access tokens
        expired_access_tokens = AccessToken.objects.filter(
            expires__lt=cutoff_date
        )
        access_count = expired_access_tokens.count()

        if access_count > 0:
            self.stdout.write(f"Found {access_count} expired access tokens")
            if not dry_run:
                expired_access_tokens.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"Deleted {access_count} expired access tokens")
                )

        # Clean up expired refresh tokens (Django OAuth Toolkit doesn't have is_revoked field by default)
        expired_refresh_tokens = RefreshToken.objects.filter(
            access_token__expires__lt=cutoff_date
        )
        refresh_count = expired_refresh_tokens.count()

        if refresh_count > 0:
            self.stdout.write(f"Found {refresh_count} expired refresh tokens")
            if not dry_run:
                expired_refresh_tokens.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"Deleted {refresh_count} expired refresh tokens")
                )
        
        # Clean up expired grants
        expired_grants = Grant.objects.filter(
            expires__lt=timezone.now()
        )
        grant_count = expired_grants.count()
        
        if grant_count > 0:
            self.stdout.write(f"Found {grant_count} expired grants")
            if not dry_run:
                expired_grants.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"Deleted {grant_count} expired grants")
                )
        
        # Clean up old security events if requested
        if cleanup_events:
            event_cutoff = timezone.now() - timedelta(days=90)
            old_events = SecurityEvent.objects.filter(created_at__lt=event_cutoff)
            event_count = old_events.count()
            
            if event_count > 0:
                self.stdout.write(f"Found {event_count} old security events")
                if not dry_run:
                    old_events.delete()
                    self.stdout.write(
                        self.style.SUCCESS(f"Deleted {event_count} old security events")
                    )
        
        total_cleaned = access_count + refresh_count + grant_count
        if cleanup_events:
            total_cleaned += event_count
        
        if total_cleaned == 0:
            self.stdout.write(self.style.SUCCESS("No expired tokens found to clean up"))
        else:
            action = "Would delete" if dry_run else "Deleted"
            self.stdout.write(
                self.style.SUCCESS(f"{action} {total_cleaned} total items")
            )
