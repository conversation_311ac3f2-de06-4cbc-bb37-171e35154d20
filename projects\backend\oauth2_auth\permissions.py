from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
from oauth2_provider.contrib.rest_framework import TokenHasScope
from .models import DeviceToken
from .utils import log_security_event, get_client_ip
from user.permissions import UserStatusMixin
import logging

logger = logging.getLogger(__name__)


class OAuth2ScopePermission(TokenHasScope):
    """
    Enhanced OAuth2 scope-based permission with logging
    """
    
    def has_permission(self, request, view):
        # Call parent implementation
        has_permission = super().has_permission(request, view)
        
        if not has_permission and hasattr(request, 'auth') and request.auth:
            # Log insufficient scope
            token_scopes = set(request.auth.scope.split()) if request.auth.scope else set()
            required_scopes = set(self.required_scopes)
            
            log_security_event(
                user=request.user if request.user.is_authenticated else None,
                event_type='insufficient_scope',
                description=f'Insufficient OAuth2 scope. Required: {required_scopes}, Available: {token_scopes}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                application=request.auth.application if hasattr(request.auth, 'application') else None,
                metadata={
                    'required_scopes': list(required_scopes),
                    'available_scopes': list(token_scopes),
                    'endpoint': request.path
                }
            )
        
        return has_permission

class TrustedDevicePermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission class requiring trusted device
    """
    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"TrustedDevice permission denied for {request.user.email}: {reason}")
            return False
        
        # Get device ID from OAuth2 token or request headers
        device_id = None
        if hasattr(request, 'auth') and hasattr(request.auth, 'device_id'):
            device_id = request.auth.device_id
        else:
            device_id = request.META.get('HTTP_X_DEVICE_ID')
        
        if not device_id:
            log_security_event(
                user=request.user,
                event_type='device_required',
                description='Device identification required but not provided',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                application=getattr(request, 'auth', None).application if hasattr(request, 'auth') and hasattr(request.auth, 'application') else None,
                metadata={'endpoint': request.path}
            )
            return False
        
        try:
            device = DeviceToken.objects.get(user=request.user, device_id=device_id)
            is_trusted = device.is_trust_valid()
            
            if not is_trusted:
                log_security_event(
                    user=request.user,
                    event_type='untrusted_device_access',
                    description=f'Access attempt from untrusted device: {device.device_name}',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    device_id=device_id,
                    application=getattr(request, 'auth', None).application if hasattr(request, 'auth') and hasattr(request.auth, 'application') else None,
                    metadata={
                        'device_name': device.device_name,
                        'device_type': device.device_type,
                        'endpoint': request.path
                    }
                )
            
            return is_trusted
            
        except DeviceToken.DoesNotExist:
            log_security_event(
                user=request.user,
                event_type='unregistered_device_access',
                description=f'Access attempt from unregistered device: {device_id}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                device_id=device_id,
                application=getattr(request, 'auth', None).application if hasattr(request, 'auth') and hasattr(request.auth, 'application') else None,
                metadata={'endpoint': request.path}
            )
            return False


class SensitiveOperationPermission(permissions.BasePermission):
    """
    Permission class for sensitive operations requiring multiple security checks
    """
    # TODO: NEED to use something like this please
    
    def has_permission(self, request, view):
        # Combine multiple permission checks
        permission_classes = [
            OAuth2ScopePermission,
            TrustedDevicePermission,
        ]
        
        for permission_class in permission_classes:
            permission = permission_class()
            if hasattr(permission, 'required_scopes'):
                permission.required_scopes = ['write']  # Require write scope for sensitive operations
            
            if not permission.has_permission(request, view):
                return False
        
        # Log sensitive operation access
        log_security_event(
            user=request.user,
            event_type='sensitive_operation_access',
            description=f'Sensitive operation accessed: {request.path}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            application=getattr(request, 'auth', None).application if hasattr(request, 'auth') and hasattr(request.auth, 'application') else None,
            metadata={
                'method': request.method,
                'endpoint': request.path
            }
        )
        
        return True


class ReadOnlyPermission(OAuth2ScopePermission):
    """Permission for read-only operations"""
    required_scopes = ['read']


class WritePermission(OAuth2ScopePermission):
    """Permission for write operations"""
    required_scopes = ['write']


class ProfilePermission(OAuth2ScopePermission):
    """Permission for profile access"""
    required_scopes = ['profile']


class EmailPermission(OAuth2ScopePermission):
    """Permission for email access"""
    required_scopes = ['email']


class OpenIDPermission(OAuth2ScopePermission):
    """Permission for OpenID Connect"""
    # TODO: NEED to use something like this please
    required_scopes = ['openid']


# Role-based permissions
class FarmerPermission(OAuth2ScopePermission, UserStatusMixin):
    """Permission for farmer role operations"""
    # TODO: NEED to use something like this please
    required_scopes = ['farmer']

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user status
        if hasattr(request, 'user') and request.user.is_authenticated:
            is_valid, reason = self.check_user_status(request.user)
            if not is_valid:
                logger.warning(f"Farmer permission denied for {request.user.email}: {reason}")
                return False

            return request.user.role == 'farmer'

        return False


class TraderPermission(OAuth2ScopePermission, UserStatusMixin):
    """Permission for trader role operations"""
    # TODO: NEED to use something like this please
    required_scopes = ['trader']

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user status and role
        if hasattr(request, 'user') and request.user.is_authenticated:
            is_valid, reason = self.check_user_status(request.user)
            if not is_valid:
                logger.warning(f"Trader permission denied for {request.user.email}: {reason}")
                return False

            return request.user.role == 'trader'

        return False


class ManufacturerPermission(OAuth2ScopePermission, UserStatusMixin):
    """Permission for manufacturer role operations"""
    # TODO: NEED to use something like this please
    required_scopes = ['manufacturer']

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user status and role
        if hasattr(request, 'user') and request.user.is_authenticated:
            is_valid, reason = self.check_user_status(request.user)
            if not is_valid:
                logger.warning(f"Manufacturer permission denied for {request.user.email}: {reason}")
                return False

            return request.user.role == 'manufacturer'

        return False


class AdminPermission(OAuth2ScopePermission):
    """Permission for admin role operations"""
    required_scopes = ['admin']

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user role and admin status
        if hasattr(request, 'user') and request.user.is_authenticated:
            return request.user.role == 'admin' or request.user.is_staff or request.user.is_superuser

        return False


class AnyRolePermission(OAuth2ScopePermission):
    """Permission that allows any authenticated user with proper scope"""
    required_scopes = ['read']

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Allow any authenticated user
        return hasattr(request, 'user') and request.user.is_authenticated


class OwnerOrAdminPermission(permissions.BasePermission):
    """
    Permission that allows access to owners of objects or admins
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Admin users can access everything
        if request.user.is_staff or request.user.is_superuser or request.user.role == 'admin':
            return True

        # Check if user owns the object
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'owner'):
            return obj.owner == request.user
        elif hasattr(obj, 'created_by'):
            return obj.created_by == request.user

        return False


class ReadWritePermission(permissions.BasePermission):
    """
    Permission class that requires different scopes for read vs write operations
    """
    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        # Determine required scope based on HTTP method
        if request.method in permissions.SAFE_METHODS:
            # Read operations require 'read' scope
            permission = ReadOnlyPermission()
        else:
            # Write operations require 'write' scope
            permission = WritePermission()

        return permission.has_permission(request, view)


class CropManagementPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for crop management operations
    Business Rule: Traders can create crops from farmers, farmers/traders/manufacturers can view
    """
    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"Crop management permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # Read operations - farmers, traders, manufacturers can view crops
        if request.method in permissions.SAFE_METHODS:
            return user_role in ['farmer', 'trader', 'manufacturer', 'admin']

        # Write operations - farmers can create their own crops, traders can create from farmers
        else:
            return user_role in ['farmer', 'trader', 'admin']

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == 'admin' or request.user.is_staff:
            return True

        # Read operations - all authenticated users can read
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write operations
        if user_role == 'farmer':
            # Farmers can only modify their own crops
            if hasattr(obj, 'farmer'):
                return obj.farmer == request.user
            elif hasattr(obj, 'user'):
                return obj.user == request.user
        elif user_role == 'trader':
            # Traders can create/modify crops from farmers (business logic handled in views)
            return True

        return False


class TransactionPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for transaction operations
    Business Rule: Any authenticated user can create transactions, users can only view their own
    """
    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"Transaction permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # All authenticated users can view and create transactions
        return user_role in ['farmer', 'trader', 'manufacturer', 'admin']

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == 'admin' or request.user.is_staff:
            return True

        # Users can only access transactions they own or are involved in
        if hasattr(obj, 'buyer') and hasattr(obj, 'seller'):
            return request.user in [obj.buyer, obj.seller]
        elif hasattr(obj, 'user'):
            return obj.user == request.user

        return False


class InventoryPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for inventory operations
    Business Rule: Traders own inventory, manufacturers can view trader inventory
    """
    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"Inventory permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # Read operations - traders and manufacturers can view inventory
        if request.method in permissions.SAFE_METHODS:
            return user_role in ['trader', 'manufacturer', 'admin']

        # Write operations - only traders can manage their own inventory
        else:
            return user_role in ['trader', 'admin']

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == 'admin' or request.user.is_staff:
            return True

        # Read operations
        if request.method in permissions.SAFE_METHODS:
            # Traders can view their own inventory, manufacturers can view any trader's inventory
            if user_role == 'trader':
                if hasattr(obj, 'trader'):
                    return obj.trader == request.user
                elif hasattr(obj, 'owner'):
                    return obj.owner == request.user
                elif hasattr(obj, 'user'):
                    return obj.user == request.user
            elif user_role == 'manufacturer':
                # Manufacturers can view any trader's inventory for purchasing
                return True

        # Write operations - only traders can manage their own inventory
        else:
            if user_role == 'trader':
                if hasattr(obj, 'trader'):
                    return obj.trader == request.user
                elif hasattr(obj, 'owner'):
                    return obj.owner == request.user
                elif hasattr(obj, 'user'):
                    return obj.user == request.user

        return False


class ProofOfUnlockPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for proof-of-unlock operations
    Business Rule: Manufacturers/traders can create, buyer-seller can view, both can update milestones
    """
    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(f"ProofOfUnlock permission denied for {request.user.email}: {reason}")
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can access proof-of-unlock
        return user_role in ['manufacturer', 'trader', 'admin']

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == 'admin' or request.user.is_staff:
            return True

        # Users can only access proof-of-unlock transactions they're involved in
        if hasattr(obj, 'buyer_id') and hasattr(obj, 'seller_id'):
            return request.user.id in [obj.buyer_id, obj.seller_id]
        elif hasattr(obj, 'buyer') and hasattr(obj, 'seller'):
            return request.user in [obj.buyer, obj.seller]
        elif hasattr(obj, 'user'):
            return obj.user == request.user

        return False
