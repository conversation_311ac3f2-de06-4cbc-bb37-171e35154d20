"""
Django management command to set up default OAuth2 clients
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from oauth2_auth.client_management import oauth2_client_manager
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up default OAuth2 clients for frontend applications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--environment',
            type=str,
            default='development',
            choices=['development', 'production'],
            help='Environment to set up clients for (default: development)'
        )
        parser.add_argument(
            '--client-type',
            type=str,
            choices=['farmer', 'trader', 'manufacturer', 'admin', 'all'],
            default='all',
            help='Specific client type to create (default: all)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing clients'
        )

    def handle(self, *args, **options):
        environment = options['environment']
        client_type = options['client_type']
        force = options['force']

        self.stdout.write(
            self.style.SUCCESS(f'Setting up OAuth2 clients for {environment} environment...')
        )

        try:
            if client_type == 'all':
                # Set up all default clients
                results = oauth2_client_manager.setup_default_clients()
                
                for client_name, result in results.items():
                    if result.startswith('ERROR'):
                        self.stdout.write(
                            self.style.ERROR(f'Failed to create {client_name}: {result}')
                        )
                    else:
                        self.stdout.write(
                            self.style.SUCCESS(f'Created {client_name} client: {result}')
                        )
            else:
                # Set up specific client
                application = oauth2_client_manager.create_frontend_client(client_type, environment)
                self.stdout.write(
                    self.style.SUCCESS(f'Created {client_type} client: {application.client_id}')
                )

            self.stdout.write(
                self.style.SUCCESS('OAuth2 client setup completed successfully!')
            )

        except Exception as e:
            logger.error(f"OAuth2 client setup failed: {str(e)}")
            raise CommandError(f'Failed to set up OAuth2 clients: {str(e)}')
