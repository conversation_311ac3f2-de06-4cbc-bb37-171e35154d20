"""
OAuth2 Client Management System
Handles creation, management, and security of OAuth2 applications/clients
"""

import secrets
import logging
from typing import Dict, List, Optional, Tuple
from django.contrib.auth import get_user_model
from django.conf import settings
from django.utils import timezone
from oauth2_provider.models import Application
from .utils import log_security_event
from .email_service import email_service

logger = logging.getLogger(__name__)
User = get_user_model()


class OAuth2ClientManager:
    """
    Centralized OAuth2 client management system
    """
    
    # Predefined client configurations for different frontend applications
    FRONTEND_CLIENTS = {
        'farmer': {
            'name': 'Agritram Farmer App',
            'redirect_uris': [
                'http://localhost:3000/auth/callback/',
                'https://farmer.agritram.com/auth/callback/',
            ],
            'scopes': ['read', 'write', 'profile', 'email', 'farmer'],
            'description': 'Frontend application for farmers'
        },
        'trader': {
            'name': 'Agritram Trader App',
            'redirect_uris': [
                'http://localhost:3001/auth/callback/',
                'https://trader.agritram.com/auth/callback/',
            ],
            'scopes': ['read', 'write', 'profile', 'email', 'trader'],
            'description': 'Frontend application for traders'
        },
        'manufacturer': {
            'name': 'Agritram Manufacturer App',
            'redirect_uris': [
                'http://localhost:3002/auth/callback/',
                'https://manufacturer.agritram.com/auth/callback/',
            ],
            'scopes': ['read', 'write', 'profile', 'email', 'manufacturer'],
            'description': 'Frontend application for manufacturers'
        },
        'admin': {
            'name': 'Agritram Admin Dashboard',
            'redirect_uris': [
                'http://localhost:3003/auth/callback/',
                'https://admin.agritram.com/auth/callback/',
            ],
            'scopes': ['read', 'write', 'profile', 'email', 'admin'],
            'description': 'Admin dashboard application'
        }
    }
    
    def __init__(self):
        self.default_scopes = ['read', 'write', 'profile', 'email']
    
    def create_client_for_user(self, user, client_type: str = 'user', **kwargs) -> Application:
        """
        Create an OAuth2 client application for a specific user
        """
        try:
            # Generate secure client credentials
            client_id = self._generate_client_id(user, client_type)
            client_secret = self._generate_client_secret()
            
            # Determine redirect URIs
            redirect_uris = kwargs.get('redirect_uris', [])
            if not redirect_uris:
                redirect_uris = [f"{settings.FRONTEND_URL}/auth/callback/"]
            
            # Create application
            application = Application.objects.create(
                name=kwargs.get('name', f"Agritram App - {user.name}"),
                user=user,
                client_type=Application.CLIENT_CONFIDENTIAL,
                authorization_grant_type=Application.GRANT_AUTHORIZATION_CODE,
                client_id=client_id,
                client_secret=client_secret,
                redirect_uris=' '.join(redirect_uris),
                post_logout_redirect_uris=kwargs.get('post_logout_redirect_uris', ''),
                algorithm=kwargs.get('algorithm', 'RS256'),
            )
            
            # Log client creation
            log_security_event(
                user=user,
                event_type='oauth2_client_created',
                description=f'OAuth2 client created: {application.name}',
                metadata={
                    'client_id': client_id,
                    'client_type': client_type,
                    'redirect_uris': redirect_uris,
                    'application_id': application.id
                }
            )
            
            logger.info(f"Created OAuth2 client for user {user.email}: {client_id}")
            return application
            
        except Exception as e:
            logger.error(f"Failed to create OAuth2 client for user {user.email}: {str(e)}")
            raise
    
    def create_frontend_client(self, client_type: str, environment: str = 'development') -> Application:
        """
        Create predefined frontend application clients
        """
        if client_type not in self.FRONTEND_CLIENTS:
            raise ValueError(f"Unknown frontend client type: {client_type}")
        
        config = self.FRONTEND_CLIENTS[client_type]
        
        try:
            # Check if client already exists
            existing_client = Application.objects.filter(
                name=config['name'],
                client_type=Application.CLIENT_CONFIDENTIAL
            ).first()
            
            if existing_client:
                logger.info(f"Frontend client {client_type} already exists: {existing_client.client_id}")
                return existing_client
            
            # Generate client credentials
            client_id = f"agritram-{client_type}-{secrets.token_urlsafe(8)}"
            client_secret = self._generate_client_secret()
            
            # Filter redirect URIs based on environment
            redirect_uris = config['redirect_uris']
            if environment == 'production':
                redirect_uris = [uri for uri in redirect_uris if not uri.startswith('http://localhost')]
            
            # Create application
            application = Application.objects.create(
                name=config['name'],
                client_type=Application.CLIENT_CONFIDENTIAL,
                authorization_grant_type=Application.GRANT_AUTHORIZATION_CODE,
                client_id=client_id,
                client_secret=client_secret,
                redirect_uris=' '.join(redirect_uris),
                algorithm='RS256',
            )
            
            # Log client creation
            log_security_event(
                event_type='frontend_client_created',
                description=f'Frontend OAuth2 client created: {config["name"]}',
                metadata={
                    'client_id': client_id,
                    'client_type': client_type,
                    'environment': environment,
                    'redirect_uris': redirect_uris,
                    'scopes': config['scopes'],
                    'application_id': application.id
                }
            )
            
            logger.info(f"Created frontend client {client_type}: {client_id}")
            return application
            
        except Exception as e:
            logger.error(f"Failed to create frontend client {client_type}: {str(e)}")
            raise
    
    def rotate_client_secret(self, application: Application, user=None) -> str:
        """
        Rotate client secret for security
        """
        try:
            old_secret = application.client_secret
            new_secret = self._generate_client_secret()
            
            application.client_secret = new_secret
            application.save(update_fields=['client_secret'])
            
            # Log secret rotation
            log_security_event(
                user=user or application.user,
                event_type='client_secret_rotated',
                description=f'OAuth2 client secret rotated: {application.name}',
                metadata={
                    'client_id': application.client_id,
                    'application_id': application.id,
                    'rotated_by': user.email if user else 'system'
                }
            )
            
            # Send notification email if user exists
            if application.user:
                email_service.send_security_alert(
                    application.user,
                    'OAuth2 Client Secret Rotated',
                    f'The client secret for your OAuth2 application "{application.name}" has been rotated for security reasons.',
                    {'client_id': application.client_id, 'application_name': application.name}
                )
            
            logger.info(f"Rotated client secret for {application.client_id}")
            return new_secret
            
        except Exception as e:
            logger.error(f"Failed to rotate client secret for {application.client_id}: {str(e)}")
            raise
    
    def revoke_client(self, application: Application, reason: str = 'User request', user=None) -> bool:
        """
        Revoke OAuth2 client and all associated tokens
        """
        try:
            from oauth2_provider.models import AccessToken, RefreshToken, Grant
            
            # Revoke all tokens
            AccessToken.objects.filter(application=application).delete()
            RefreshToken.objects.filter(application=application).delete()
            Grant.objects.filter(application=application).delete()
            
            # Log revocation
            log_security_event(
                user=user or application.user,
                event_type='oauth2_client_revoked',
                description=f'OAuth2 client revoked: {application.name}',
                metadata={
                    'client_id': application.client_id,
                    'application_id': application.id,
                    'reason': reason,
                    'revoked_by': user.email if user else 'system'
                }
            )
            
            # Send notification email
            if application.user:
                email_service.send_security_alert(
                    application.user,
                    'OAuth2 Application Revoked',
                    f'Your OAuth2 application "{application.name}" has been revoked. Reason: {reason}',
                    {'client_id': application.client_id, 'reason': reason}
                )
            
            # Delete application
            application.delete()
            
            logger.info(f"Revoked OAuth2 client {application.client_id}: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to revoke OAuth2 client {application.client_id}: {str(e)}")
            return False
    
    def get_client_info(self, client_id: str) -> Optional[Dict]:
        """
        Get comprehensive client information
        """
        try:
            application = Application.objects.get(client_id=client_id)
            
            # Get token statistics
            from oauth2_provider.models import AccessToken, RefreshToken
            active_tokens = AccessToken.objects.filter(
                application=application,
                expires__gt=timezone.now()
            ).count()
            
            total_tokens = AccessToken.objects.filter(application=application).count()
            
            return {
                'client_id': application.client_id,
                'name': application.name,
                'client_type': application.get_client_type_display(),
                'grant_type': application.get_authorization_grant_type_display(),
                'redirect_uris': application.redirect_uris.split(),
                'created': application.created,
                'updated': application.updated,
                'user': application.user.email if application.user else None,
                'active_tokens': active_tokens,
                'total_tokens_issued': total_tokens,
                'algorithm': application.algorithm,
            }
            
        except Application.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Failed to get client info for {client_id}: {str(e)}")
            return None
    
    def list_user_clients(self, user) -> List[Dict]:
        """
        List all OAuth2 clients for a user
        """
        try:
            applications = Application.objects.filter(user=user)
            return [self.get_client_info(app.client_id) for app in applications]
        except Exception as e:
            logger.error(f"Failed to list clients for user {user.email}: {str(e)}")
            return []
    
    def _generate_client_id(self, user, client_type: str) -> str:
        """
        Generate a unique client ID
        """
        base = f"agritram-{client_type}-{user.id}"
        suffix = secrets.token_urlsafe(8)
        return f"{base}-{suffix}"
    
    def _generate_client_secret(self) -> str:
        """
        Generate a secure client secret
        """
        return secrets.token_urlsafe(32)
    
    def setup_default_clients(self) -> Dict[str, str]:
        """
        Set up default OAuth2 clients for all frontend applications
        """
        results = {}
        environment = 'development' if settings.DEBUG else 'production'
        
        for client_type in self.FRONTEND_CLIENTS.keys():
            try:
                application = self.create_frontend_client(client_type, environment)
                results[client_type] = application.client_id
            except Exception as e:
                logger.error(f"Failed to create default client {client_type}: {str(e)}")
                results[client_type] = f"ERROR: {str(e)}"
        
        return results


# Global client manager instance
oauth2_client_manager = OAuth2ClientManager()
